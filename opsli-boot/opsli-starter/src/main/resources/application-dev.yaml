## ---- 开发环境 ---- ##

# 端口设置
server:
  port: 7001

spring:
  data:
    #redis 配置
    redis:
      database: 8
      host: *************
      password: 'PASS_123456_word'
      port: 6379

  # 数据库配置
  datasource:
    # 数据库连接池监控
    druid:
      stat-view-servlet:
        # 数据库监控开关
        enabled: true
        # 登录用户名/密码
        login-username: admin
        login-password: 123456
        # IP 白名单，没有配置或者为空，则允许所有访问
        allow:
        # IP 黑名单，若白名单也存在，则优先使用
        deny:
    # 多数据源
    dynamic:
      #主数据源
      #设置默认的数据源或者数据源组,默认值即为master,如果你主从默认下主库的名称就是master可不定义此项。
      #primary: master
      datasource:
        master:
          # url: ******************************************************************************************************************************************************************************
          # username: opsli-boot
          # password: 12345678
          # driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************************************************************
          username: zhangxin
          password: PASS_123456_word
          driver-class-name: com.mysql.cj.jdbc.Driver
        # 多数据源配置
        #slave-datasource:
          #url: **************************************************************************************************************************************************************************
          #username: root
          #password: 12345678
          #driver-class-name: com.mysql.cj.jdbc.Driver

# Redisson 分布式锁
redisson:
  lock:
    server:
      enable: true
      type: standalone
      address: "${spring.data.redis.host}:${spring.data.redis.port}"
      password: "${spring.data.redis.password}"
      database: ${spring.data.redis.database}

#mybatis-plus 插件
mybatis-plus:
  configuration:
    # 打印SQL 开发测试使用 生产关闭 ***
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# swagger接口文档
springdoc:
  swagger-ui:
    enabled: true

# opsli 自定义配置
opsli:
  request-log:
    enabled: true
    include-headers: true
    include-body: true
  # 演示模式
  enable-demo: false
  # 代码生成器
  generator:
    enable: true
  # web 上传文件路径
  web:
    upload-path: var/files

# 日志
logging:
  level:
    root: info

