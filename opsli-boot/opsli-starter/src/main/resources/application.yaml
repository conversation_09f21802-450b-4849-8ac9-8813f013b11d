# tomcat 配置
server:
  tomcat:
    # Tomcat 工作目录路径
    #basedir:
    # Tomcat 编码
    uri-encoding: UTF-8
    # 对于 Tomcat 头文件不限制大小
    max-swallow-size: -1
  servlet:
    context-path: /opsli-boot
    ## 专门针对 Controller层接口路径前缀全局配置
    api:
      path:
        global-prefix: /api
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

# actuator 暴露端点
#management:
#  endpoints:
#    web:
#      exposure:
#        include: metrics,httptrace

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: opsli-boot
  profiles:
    #此处由maven的环境选择决定
    active: "@spring.active@"
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  #静态资源
  #json 时间戳统一转换
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  #开启aop
  aop:
    proxy-target-class: true
  # 自动注入
#  autoconfigure:
#    exclude:
#      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  #
  #main:
  #  allow-bean-definition-overriding: true
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/, classpath:/static/, classpath:/public/,file:${opsli.web.upload-path}
  mvc:
    #Spring Boot 2.6+后映射匹配的默认策略已从AntPathMatcher更改为PathPatternParser,需要手动指定为ant-path-matcher
    pathmatch:
      matching-strategy: ant_path_matcher
  # 缓存配置项
  cache:
    # 前缀
    prefix: opsli
  data:
    redis:
      # Lettuce 连接池优化配置
      lettuce:
        pool:
          # 连接池配置优化建议
          max-active: 50      # 根据并发量调整，避免连接不够用
          max-idle: 20        # 保持足够的空闲连接，减少创建连接开销
          min-idle: 10        # 保证最小连接数，避免冷启动问题
          max-wait: 3000ms    # 设置合理的等待时间，避免无限等待
        # 连接超时配置
        shutdown-timeout: 200ms

  # 数据库配置
  datasource:
    # 数据库连接池监控
    druid:
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      # 监控配置
      filters: stat,wall,slf4j    # 启用过滤器
      # 慢SQL监控
      filter:
        stat:
          enabled: true
          log-slow-sql: true      # 记录慢SQL
          slow-sql-millis: 2000   # 定义慢SQL时间阈值
          merge-sql: true         # 合并相同SQL
        wall:
          enabled: true           # SQL防火墙
        slf4j:
          enabled: true           # 日志
          connection-log-enabled: false
          statement-log-enabled: false
          statement-sql-pretty-format: true
          statement-parameter-set-log-enabled: false
      # 监控Web页面
      stat-view-servlet:
        url-pattern: /druid/*
      # 连接配置
      keep-alive: true
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: .js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*

    # 多数据源
    dynamic:
      druid:
        # 初始化大小，最小，最大
        initial-size: ${spring.datasource.druid.initial-size}
        min-idle: ${spring.datasource.druid.min-idle}
        maxActive: ${spring.datasource.druid.max-active}
        # 配置获取连接等待超时的时间
        maxWait: ${spring.datasource.druid.max-wait}
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: ${spring.datasource.druid.test-while-idle}
        testOnBorrow: ${spring.datasource.druid.test-on-borrow}
        testOnReturn: ${spring.datasource.druid.test-on-return}
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的 Filter，去掉后监控界面 SQL 无法统计，wall 用于防火墙
        filters: ${spring.datasource.druid.filters}


#mybatis-plus 插件
mybatis-plus:
  #所有的mapper映射文件
  mapper-locations:
    - classpath*:org/opsli/modulars/**/mapper/xml/*.xml
  # 实体扫描，多个package 继续往后跟
  typeAliasesPackage: org.opsli.modulars.**.entity
  # 实体扫描  BaseEntity 的子类对象
  typeAliasesSuperType: org.opsli.core.base.entity.BaseEntity
  global-config:
    # 关闭Banner
    banner: false
    db-config:
      # 主键类型 id-type:
      # 数据库ID自增 - auto | 0   未设置主键类型 - none | 1   手动输入ID - input | 2  数字类型唯一ID - assign_id | 3  唯一ID一UUID - assign_uuid | 4
      id-type: assign_id
      logic-delete-field: deleted  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
      # 更新策略 - 防止更新 null 值到数据字段中 空字符串可以更新
      # 不要改！！！如果想要非空修改 直接在对应字段上加注解 全局不允许 可空数据修改
      update-strategy: not_empty
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true



# OPSLI 自定义配置
opsli:
  # 请求日志
  request-log:
    enabled: false 
    include-headers: false
    include-body: false

  # 系统名称
  system-name: "OPSLI 快速开发平台"
  # 系统启动时间 为空则默认 真实当前系统启动时间
  system-starter-time: "2020-09-10 00:00:00"

  # 软防火墙
  waf:
    # 开启防护
    enable: true
    # 开启XSS 防护
    xss-filter: true
    # 开启sql注入防护 (目前用不着sql防护 mybatis-plus就已经很完善了)
    sql-filter: false
    # 设置xss防护过滤器的优先级，值越小优先级越高（不要超过 shiroFilter）
    order: 0
    # 设置xss防护的url拦截路径
    url-patterns: "/*"
    # 排除过滤URL
    url-exclusion:
      - "/api/v1/generator/template/insertAndDetail"
      - "/api/v1/generator/template/updateAndDetail"
      - "/static/files/"
      - "/doc.html"

  # 认证
  auth:
    # 凭证过期时间（天）-1 默认不过期，（如果要设置过期请先完成用户无需登录修改密码操作）
    credentials-expired: -1
    # 排除过滤URL
    url-exclusion:
      permit-all:
        - "/static/files/**"
        - "/v3/api-docs/**"
        - "/swagger-ui.html"
        - "/swagger-ui/**"
        - "/doc.html"
        - "/swagger-resources/**"
        - "/webjars/**"
        - "/app/**"
        - "/swagger/*"

        - "/druid/**"
        - "/captcha"
        - "/system/slipCount"
        - "/system/login"
        - "/system/login-by-code"
        - "/api/*/common/public-key"
        - "/api/*/common/email/create-code"
        - "/api/*/common/mobile/create-code"

    # 超级管理员账号
    super-admin: system
    # 重置默认密码 密码至少包含大小写字母，数字，且不少于6位
    # 优先使用 option 缓存， 如果缓存为空则使用配置文件
    default-pass: Aa123456
    # Token
    token:
      # 默认加密盐值（每个系统需要不一致防止被破解）
      secret: 53c0e33c9d4c5538969abf2fcf48351d
      # 有效时间 （分钟） 2小时
      effective-time: 120

    # 登录设置
    login:
      # 续命模式 (开启续命模式后 在有效时间内 访问任意接口 则自动续命)
      revive-mode: false
      # 限制登录数量 -1 为无限大
      limit-count: -1
      # 限制登录拒绝策略 after为后者 before为前者
      limit-refuse: after
      # 失败次数
      slip-count: 5
      # 失败N次后弹出验证码 （超过验证码阈值 弹出验证码）
      slip-verify-count: 3
      # 失败锁定时间(秒)
      slip-lock-speed: 300

  # Excel
  excel:
    # Excel 最大导出操作数量 防止OOM  -1为无限制
    export-max-count: 100000

# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    path: /doc.html
    persist-authorization: true # 持久化授权信息
    display-request-duration: true # 显示请求耗时
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
    enabled: ${springdoc.swagger-ui.enabled}

  # 公共配置
  cache:
    disabled: false  # 禁用缓存
  show-actuator: false  # 是否为Actuator端点生成API文档
  writer-with-default-pretty-printer: true  # 格式化JSON输出
  model-and-view-allowed: true  # 允许ModelAndView作为返回类型
  override-with-generic-response: true  # 使用泛型响应覆盖
