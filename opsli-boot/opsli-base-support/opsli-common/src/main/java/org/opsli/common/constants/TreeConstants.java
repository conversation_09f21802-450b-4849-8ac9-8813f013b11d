/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.common.constants;

/**
 * 菜单常量
 *
 * <AUTHOR>
 * @date 2021年3月10日15:50:16
 */
public final class TreeConstants {

    /** 是否包含子集 */
    public static final String HAS_CHILDREN = "hasChildren";


    /** 是否是叶子节点 */
    public static final String IS_LEAF = "isLeaf";


    private TreeConstants(){}
}
