package org.opsli.core.general.interceptor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.opsli.core.autoconfigure.properties.RequestLogProperties;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @BelongsProject: opsli-boot
 * @BelongsPackage: org.opsli.core.general.interceptor
 * @Author: Parker
 * @CreateTime: 2020-09-17 12:01
 * @Description: 全局请求日志拦截器
 */
@Slf4j
@Component
public class RequestLogInterceptor implements HandlerInterceptor {

    @Resource
    private RequestLogProperties logProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!logProperties.isEnabled()) {
            return true;
        }

        // 计时
        TimeInterval timer = DateUtil.timer();

        // Log
        log.info("------------- \"{}\" 请求开始 -------------", request.getRequestURI());
        log.info("请求URL: {}", request.getRequestURL().toString());
        log.info("请求方式: {}", request.getMethod());

        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            log.info("请求方法: {}.{}", handlerMethod.getBeanType().getSimpleName(), handlerMethod.getMethod().getName());
        }

        // 打印请求头
        if (logProperties.isIncludeHeaders()) {
            Map<String, String> headers = Collections.list(request.getHeaderNames())
                    .stream()
                    .collect(Collectors.toMap(h -> h, request::getHeader));
            log.info("请求头: \n{}", JSON.toJSONString(headers, true));
        }

        // 打印请求体
        if (logProperties.isIncludeBody()) {
            Map<String, String[]> parameterMap = request.getParameterMap();
            if (!parameterMap.isEmpty()) {
                log.info("请求参数(Body): \n{}", JSON.toJSONString(parameterMap, true));
            }
        }

        // 保存请求开始时间
        request.setAttribute("requestStartTime", timer);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        if (!logProperties.isEnabled()) {
            return;
        }

        // 获得请求开始时间
        TimeInterval timer = (TimeInterval) request.getAttribute("requestStartTime");

        // Log
        log.info("------------- \"{}\" 请求结束 => 耗时: {}ms -------------",
                request.getRequestURI(), timer.interval());

        if (ex != null) {
            log.error("请求异常: ", ex);
        }
    }
}

