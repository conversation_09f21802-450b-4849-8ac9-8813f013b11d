package org.opsli.core.autoconfigure.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @BelongsProject: opsli-boot
 * @BelongsPackage: org.opsli.core.autoconfigure.properties
 * @Author: Parker
 * @CreateTime: 2020-09-17 12:01
 * @Description: 全局请求日志配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "opsli.request.log")
public class RequestLogProperties {

    /** 是否开启 */
    private boolean enabled = true;

    /** 是否显示请求头 */
    private boolean includeHeaders = false;

    /** 是否显示请求体 */
    private boolean includeBody = true;

    /** 排除的URL列表 */
    private List<String> excludeUrlPatterns = Collections.singletonList("/actuator/**");

}
