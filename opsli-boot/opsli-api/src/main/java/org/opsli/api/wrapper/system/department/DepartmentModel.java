/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.department;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.opsli.api.base.warpper.ApiWrapper;
import org.opsli.plugins.excel.annotation.ExcelInfo;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Date;

/**
 * 部门管理 - 按月份 Model
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Schema(description = "部门管理 - 按月份 Model")
@Data
@EqualsAndHashCode(callSuper = true)
public class DepartmentModel extends ApiWrapper {

    /** 租户ID */
    @Schema(description = "租户ID")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;
    
    /** 数据月份 */
    @Schema(description = "数据月份")
    @NotNull(message = "数据月份不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    @ExcelProperty(value = "数据月份", order = 1)
    @ExcelInfo
    private Date dataMonth;
    
    /** 部门名称 */
    @Schema(description = "部门名称")
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 255, message = "部门名称长度不能超过255个字符")
    @ExcelProperty(value = "部门名称", order = 2)
    @ExcelInfo
    private String name;
    
    /** 部门编码 */
    @Schema(description = "部门编码")
    @NotBlank(message = "部门编码不能为空")
    @Size(max = 100, message = "部门编码长度不能超过100个字符")
    @ExcelProperty(value = "部门编码", order = 3)
    @ExcelInfo
    private String deptCode;
    
    /** 父部门ID */
    @Schema(description = "父部门ID")
    @ExcelProperty(value = "父部门ID", order = 4)
    @ExcelInfo
    private Long parentDepartmentId;
    
    /** 父部门名称 - 仅用于展示 */
    @Schema(description = "父部门名称")
    @ExcelProperty(value = "父部门名称", order = 5)
    @ExcelInfo
    private String parentDepartmentName;
    
    /** 状态：1-启用，0-禁用 */
    @Schema(description = "状态：1-启用，0-禁用")
    @NotNull(message = "状态不能为空")
    @ExcelProperty(value = "状态", order = 6)
    @ExcelInfo(dictType = "common_status")
    private Integer status;
    
    /** 负责人ID */
    @Schema(description = "负责人ID")
    @ExcelProperty(value = "负责人ID", order = 7)
    @ExcelInfo
    private Long responsiblePerson;
    
    /** 职责描述 */
    @Schema(description = "职责描述")
    @Size(max = 500, message = "职责描述长度不能超过500个字符")
    @ExcelProperty(value = "职责描述", order = 8)
    @ExcelInfo
    private String responsibilities;
}