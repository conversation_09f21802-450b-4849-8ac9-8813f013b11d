/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.position;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.opsli.api.base.warpper.ApiWrapper;
import org.opsli.plugins.excel.annotation.ExcelInfo;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import java.util.Date;

/**
 * 职位管理 - 按月份 Model
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Schema(description = "职位管理 - 按月份 Model")
@Data
@EqualsAndHashCode(callSuper = true)
public class PositionModel extends ApiWrapper {

    /** 租户ID */
    @Schema(description = "租户ID")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;
    
    /** 数据月份 */
    @Schema(description = "数据月份")
    @NotNull(message = "数据月份不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    @ExcelProperty(value = "数据月份", order = 1)
    @ExcelInfo
    private Date dataMonth;
    
    /** 职位编码 */
    @Schema(description = "职位编码")
    @NotBlank(message = "职位编码不能为空")
    @Size(max = 50, message = "职位编码长度不能超过50个字符")
    @ExcelProperty(value = "职位编码", order = 2)
    @ExcelInfo
    private String positionCode;
    
    /** 职位名称 */
    @Schema(description = "职位名称")
    @NotBlank(message = "职位名称不能为空")
    @Size(max = 255, message = "职位名称长度不能超过255个字符")
    @ExcelProperty(value = "职位名称", order = 3)
    @ExcelInfo
    private String title;
    
    /** 职位描述 */
    @Schema(description = "职位描述")
    @Size(max = 1000, message = "职位描述长度不能超过1000个字符")
    @ExcelProperty(value = "职位描述", order = 4)
    @ExcelInfo
    private String description;
    
    /** 职位要求 */
    @Schema(description = "职位要求")
    @Size(max = 1000, message = "职位要求长度不能超过1000个字符")
    @ExcelProperty(value = "职位要求", order = 5)
    @ExcelInfo
    private String requirements;
    
    /** 关联部门ID */
    @Schema(description = "关联部门ID")
    @ExcelProperty(value = "关联部门ID", order = 6)
    @ExcelInfo
    private Long departmentId;
    
    /** 关联部门名称 - 仅用于展示 */
    @Schema(description = "关联部门名称")
    @ExcelProperty(value = "关联部门名称", order = 7)
    @ExcelInfo
    private String departmentName;
    
    /** 职位级别 */
    @Schema(description = "职位级别")
    @Size(max = 50, message = "职位级别长度不能超过50个字符")
    @ExcelProperty(value = "职位级别", order = 8)
    @ExcelInfo
    private String positionLevel;
    
    /** 编制人数 */
    @Schema(description = "编制人数")
    @Min(value = 0, message = "编制人数不能小于0")
    @ExcelProperty(value = "编制人数", order = 9)
    @ExcelInfo
    private Integer headcount;
    
    /** 状态：1-启用，0-禁用 */
    @Schema(description = "状态：1-启用，0-禁用")
    @NotNull(message = "状态不能为空")
    @ExcelProperty(value = "状态", order = 10)
    @ExcelInfo(dictType = "common_status")
    private Integer status;
}
