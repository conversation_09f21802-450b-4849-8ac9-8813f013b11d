/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.web.system.department;

import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.wrapper.system.department.DepartmentModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 部门管理 - 按月份 API
 *
 * 对外 API 直接 暴露 @GetMapping 或者 @PostMapping
 * 对内也推荐 单机版 不需要设置 Mapping 但是调用方法得从Controller写起
 *
 * 这样写法虽然比较绕，但是当单体项目想要改造微服务架构时 时非常容易的
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface DepartmentApi {

    /** 标题 */
    String TITLE = "部门管理";
    /** 子标题 */
    String SUB_TITLE = "部门管理 - 按月份";

    /**
     * 部门管理 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @GetMapping("/get")
    ResultWrapper<DepartmentModel> get(DepartmentModel model);

    /**
     * 部门管理 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findPage")
    ResultWrapper<?> findPage(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest request
    );

    /**
     * 部门管理 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/insert")
    ResultWrapper<?> insert(@RequestBody DepartmentModel model);

    /**
     * 部门管理 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/update")
    ResultWrapper<?> update(@RequestBody DepartmentModel model);

    /**
     * 部门管理 删除
     * @param id ID
     * @return ResultWrapper
     */
    @PostMapping("/del")
    ResultWrapper<?> del(String id);

    /**
     * 部门管理 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @PostMapping("/delAll")
    ResultWrapper<?> delAll(String ids);

    /**
     * 根据父部门ID获取子部门列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param parentId 父部门ID
     * @return ResultWrapper
     */
    @GetMapping("/findByParentId")
    ResultWrapper<List<DepartmentModel>> findByParentId(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth,
            @RequestParam("parentId") Long parentId
    );

    /**
     * 检查部门名称是否唯一
     *
     * @param model 部门模型
     * @return ResultWrapper
     */
    @PostMapping("/checkNameUnique")
    ResultWrapper<Boolean> checkNameUnique(@RequestBody DepartmentModel model);

    /**
     * 检查部门编码是否唯一
     *
     * @param model 部门模型
     * @return ResultWrapper
     */
    @PostMapping("/checkCodeUnique")
    ResultWrapper<Boolean> checkCodeUnique(@RequestBody DepartmentModel model);
}