/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.web.system.menu;

import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.wrapper.system.menu.MenuFullModel;
import org.opsli.api.wrapper.system.menu.MenuModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 菜单管理 API
 *
 * 对外 API 直接 暴露 @GetMapping 或者 @PostMapping
 * 对内也推荐 单机版 不需要设置 Mapping 但是调用方法得从Controller写起
 *
 * 这样写法虽然比较绕，但是当单体项目想要改造微服务架构时 时非常容易的
 *
 * <AUTHOR>
 * @date 2020-09-13 17:40
 */
public interface MenuApi {

    /** 标题 */
    String TITLE = "菜单管理";
    /** 子标题 */
    String SUB_TITLE = "菜单";


    /**
     * 获得列表菜单
     *
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findMenuTreePage")
    ResultWrapper<?> findMenuTreePage(HttpServletRequest request);

    /**
     * 懒加载列表菜单
     *
     * @param parentId 父节点ID
     * @return ResultWrapper
     */
    @GetMapping("/findMenuTreePageByLazy")
    ResultWrapper<?> findMenuTreePageByLazy(String parentId);

    /**
     * 懒加载菜单
     * @param parentId 父节点ID
     * @param id 自身ID （不为空 则排除自身）
     * @return ResultWrapper
     */
    @GetMapping("/findMenuTreeByLazy")
    ResultWrapper<?> findMenuTreeByLazy(String parentId, String id);

    /**
     * 获得当前用户登录菜单
     * @return ResultWrapper
     */
    @PostMapping("/findMenuTree")
    ResultWrapper<?> findMenuTree();


    /**
     * 根据 获得用户 菜单 - 权限
     *
     * @param label 标签
     * @return ResultWrapper
     */
    @GetMapping("/getMenuAndPermsTree")
    ResultWrapper<?> getMenuAndPermsTree(String label);

    /**
     * 获得集合
     * @return ResultWrapper
     */
    @GetMapping("/findList")
    ResultWrapper<List<MenuModel>> findList();


    /**
     * 菜单 查一条
     * @param parentId 父级ID
     * @return ResultWrapper
     */
    @GetMapping("/getParent")
    ResultWrapper<MenuModel> getParent(String parentId);

    /**
     * 菜单 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @GetMapping("/get")
    ResultWrapper<MenuModel> get(MenuModel model);

    /**
     * 菜单 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findPage")
    ResultWrapper<?> findPage(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest request
    );

    /**
     * 菜单 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/insert")
    ResultWrapper<?> insert(@RequestBody MenuModel model);

    /**
     * 菜单 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/update")
    ResultWrapper<?> update(@RequestBody MenuModel model);

    /**
     * 菜单 删除
     * @param id ID
     * @return ResultWrapper
     */
    @PostMapping("/del")
    ResultWrapper<?> del(String id);

    /**
     * 菜单 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @PostMapping("/delAll")
    ResultWrapper<?> delAll(String ids);


    // ================= 普通

    /**
     * 根据菜单权限 获得菜单
     * @param permissions 菜单权限
     * @return ResultWrapper
     */
    ResultWrapper<MenuModel> getByPermissions(String permissions);

    /**
     * 菜单完整 新增
     * @param menuFullModel 模型
     * @return ResultWrapper
     */
    ResultWrapper<?> saveMenuByFull(@RequestBody MenuFullModel menuFullModel);
}
