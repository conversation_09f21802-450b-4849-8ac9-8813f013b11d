/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.attendance;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.opsli.api.base.warpper.ApiWrapper;

import org.opsli.plugins.excel.annotation.ExcelInfo;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;

/**
 * 考勤管理 - 按月份 Model
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Schema(description = "考勤管理 - 按月份 Model")
@Data
@EqualsAndHashCode(callSuper = true)
public class AttendanceModel extends ApiWrapper {

    /** 租户ID */
    @Schema(description = "租户ID")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;
    
    /** 数据月份 */
    @Schema(description = "数据月份")
    @NotNull(message = "数据月份不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    @ExcelProperty(value = "数据月份", order = 1)
    @ExcelInfo
    private Date dataMonth;
    
    /** 员工ID */
    @Schema(description = "员工ID")
    @NotNull(message = "员工ID不能为空")
    @ExcelProperty(value = "员工ID", order = 2)
    @ExcelInfo
    private Long employeeId;
    
    /** 部门ID */
    @Schema(description = "部门ID")
    @NotNull(message = "部门ID不能为空")
    @ExcelProperty(value = "部门ID", order = 3)
    @ExcelInfo
    private Long departmentId;
    
    /** 考勤日期 */
    @Schema(description = "考勤日期")
    @NotNull(message = "考勤日期不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "考勤日期", order = 4)
    @ExcelInfo
    private LocalDate attendanceDate;
    
    /** 签到时间 */
    @Schema(description = "签到时间")
    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm")
    @DateTimeFormat(pattern = "HH:mm")
    @ExcelProperty(value = "签到时间", order = 5)
    @ExcelInfo
    private LocalTime checkInTime;
    
    /** 签退时间 */
    @Schema(description = "签退时间")
    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm")
    @DateTimeFormat(pattern = "HH:mm")
    @ExcelProperty(value = "签退时间", order = 6)
    @ExcelInfo
    private LocalTime checkOutTime;
    
    /** 考勤状态 */
    @Schema(description = "考勤状态")
    @NotBlank(message = "考勤状态不能为空")
    @Size(max = 50, message = "考勤状态长度不能超过50个字符")
    @ExcelProperty(value = "考勤状态", order = 7)
    @ExcelInfo(dictType = "attendance_status")
    private String status;
    
    /** 是否纳入统计 */
    @Schema(description = "是否纳入统计")
    @ExcelProperty(value = "是否纳入统计", order = 8)
    @ExcelInfo(dictType = "yes_no")
    private Integer includeInStats;
    
    /** 备注 */
    @Schema(description = "备注")
    @Size(max = 2000, message = "备注长度不能超过2000个字符")
    @ExcelProperty(value = "备注", order = 9)
    @ExcelInfo
    private String remarks;
}
