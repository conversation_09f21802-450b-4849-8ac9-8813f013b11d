/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.employee;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.opsli.api.base.warpper.ApiWrapper;
import org.opsli.plugins.excel.annotation.ExcelInfo;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;
import java.util.Date;

/**
 * 员工管理 - 按月份 Model
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Schema(description = "员工管理 - 按月份 Model")
@Data
@EqualsAndHashCode(callSuper = true)
public class EmployeeModel extends ApiWrapper {

    /** 租户ID */
    @Schema(description = "租户ID")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;
    
    /** 数据月份 */
    @Schema(description = "数据月份")
    @NotNull(message = "数据月份不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM")
    @DateTimeFormat(pattern = "yyyy-MM")
    @ExcelProperty(value = "数据月份", order = 1)
    @ExcelInfo
    private Date dataMonth;
    
    /** 员工编号 */
    @Schema(description = "员工编号")
    @NotBlank(message = "员工编号不能为空")
    @Size(max = 50, message = "员工编号长度不能超过50个字符")
    @ExcelProperty(value = "员工编号", order = 2)
    @ExcelInfo
    private String employeeNumber;
    
    /** 姓名 */
    @Schema(description = "姓名")
    @NotBlank(message = "姓名不能为空")
    @Size(max = 255, message = "姓名长度不能超过255个字符")
    @ExcelProperty(value = "姓名", order = 3)
    @ExcelInfo
    private String fullName;
    
    /** 性别 */
    @Schema(description = "性别")
    @Size(max = 10, message = "性别长度不能超过10个字符")
    @ExcelProperty(value = "性别", order = 4)
    @ExcelInfo(dictType = "gender")
    private String gender;
    
    /** 出生日期 */
    @Schema(description = "出生日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "出生日期", order = 5)
    @ExcelInfo
    private Date dateOfBirth;
    
    /** 手机号码 */
    @Schema(description = "手机号码")
    @Size(max = 20, message = "手机号码长度不能超过20个字符")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    @ExcelProperty(value = "手机号码", order = 6)
    @ExcelInfo
    private String phoneNumber;
    
    /** 身份证号 */
    @Schema(description = "身份证号")
    @Size(max = 20, message = "身份证号长度不能超过20个字符")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", message = "身份证号格式不正确")
    @ExcelProperty(value = "身份证号", order = 7)
    @ExcelInfo
    private String idCardNumber;
    
    /** 关联部门ID */
    @Schema(description = "关联部门ID")
    @NotNull(message = "关联部门不能为空")
    @ExcelProperty(value = "关联部门ID", order = 8)
    @ExcelInfo
    private Long departmentId;
    
    /** 关联部门名称 - 仅用于展示 */
    @Schema(description = "关联部门名称")
    @ExcelProperty(value = "关联部门名称", order = 9)
    @ExcelInfo
    private String departmentName;
    
    /** 主职位ID */
    @Schema(description = "主职位ID")
    @NotNull(message = "主职位不能为空")
    @ExcelProperty(value = "主职位ID", order = 10)
    @ExcelInfo
    private Long positionId;
    
    /** 主职位名称 - 仅用于展示 */
    @Schema(description = "主职位名称")
    @ExcelProperty(value = "主职位名称", order = 11)
    @ExcelInfo
    private String positionName;
    
    /** 兼岗位ID */
    @Schema(description = "兼岗位ID")
    @ExcelProperty(value = "兼岗位ID", order = 12)
    @ExcelInfo
    private Long concurrentPositionId;
    
    /** 兼岗位名称 - 仅用于展示 */
    @Schema(description = "兼岗位名称")
    @ExcelProperty(value = "兼岗位名称", order = 13)
    @ExcelInfo
    private String concurrentPositionName;
    
    /** 入职日期 */
    @Schema(description = "入职日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "入职日期", order = 14)
    @ExcelInfo
    private Date hireDate;
    
    /** 员工状态 */
    @Schema(description = "员工状态")
    @Size(max = 50, message = "员工状态长度不能超过50个字符")
    @ExcelProperty(value = "员工状态", order = 15)
    @ExcelInfo(dictType = "employee_status")
    private String status;
    
    /** 是否缴纳社保 */
    @Schema(description = "是否缴纳社保")
    @ExcelProperty(value = "是否缴纳社保", order = 16)
    @ExcelInfo(dictType = "yes_no")
    private Boolean hasSocialInsurance;
    
    /** 是否签订合同 */
    @Schema(description = "是否签订合同")
    @ExcelProperty(value = "是否签订合同", order = 17)
    @ExcelInfo(dictType = "yes_no")
    private Boolean isContractSigned;
    
    /** 是否已转正 */
    @Schema(description = "是否已转正")
    @ExcelProperty(value = "是否已转正", order = 18)
    @ExcelInfo(dictType = "yes_no")
    private Boolean isProbationPassed;
    
    /** 是否签订资料 */
    @Schema(description = "是否签订资料")
    @ExcelProperty(value = "是否签订资料", order = 19)
    @ExcelInfo(dictType = "yes_no")
    private Boolean areDocumentsSigned;
    
    /** 个人简介 */
    @Schema(description = "个人简介")
    @Size(max = 2000, message = "个人简介长度不能超过2000个字符")
    @ExcelProperty(value = "个人简介", order = 20)
    @ExcelInfo
    private String personalBio;
}
