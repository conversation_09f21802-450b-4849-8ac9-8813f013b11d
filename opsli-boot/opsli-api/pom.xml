<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>opsli-boot-parent</artifactId>
        <groupId>org.opsliframework.boot</groupId>
        <version>1.0.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>opsli-api</artifactId>
    <version>1.0.0</version>

    <dependencies>
        <!-- 需要使用着两个版本 来引入对应的模块和插件 -->
        <!-- 基础版本 ${base.version} -->
        <!-- 插件版本 ${plugins.version} -->
        <!-- 模块版本 ${modulars.version}-->

        <!-- 引入公共模块 -->
        <dependency>
            <groupId>org.opsliframework.boot</groupId>
            <artifactId>opsli-common</artifactId>
            <version>${base.version}</version>
        </dependency>

        <!-- 引入Excel插件 -->
        <dependency>
            <groupId>org.opsliframework.boot</groupId>
            <artifactId>opsli-plugins-excel</artifactId>
            <version>${plugins.version}</version>
        </dependency>

        <!-- mybatis-plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <!-- ———————————————————— 集成SwaggerApi - 开始 ———————————————————— -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <!-- ———————————————————— 集成SwaggerApi - 结束 ———————————————————— -->

    </dependencies>
</project>
