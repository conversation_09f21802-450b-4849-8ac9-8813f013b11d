SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 设置新的代码生成模板
-- ----------------------------
DROP TABLE IF EXISTS `gen_template_detail`;
CREATE TABLE `gen_template_detail`  (
                                        `id` bigint(19) NOT NULL COMMENT '主键',
                                        `parent_id` bigint(19) NOT NULL COMMENT '父级ID',
                                        `type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型 0 后端  / 1 前端',
                                        `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '路径',
                                        `file_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件名',
                                        `file_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件内容',
                                        `ignore_file_name` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '忽略文件名',
                                        `version` int(11) NOT NULL COMMENT '版本（乐观锁）',
                                        `create_by` bigint(19) NOT NULL COMMENT '创建用户',
                                        `create_time` datetime(0) NOT NULL COMMENT '创建日期',
                                        `update_by` bigint(19) NOT NULL COMMENT '修改用户',
                                        `update_time` datetime(0) NOT NULL COMMENT '修改日期',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        INDEX `creater_table_name`(`path`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成器 - 表信息' ROW_FORMAT = Dynamic;
INSERT INTO `gen_template_detail` VALUES (1469919953178763265, 1398253704724828162, '0', '${packageName}/${moduleName}/${subModuleName}/entity', '${model.tableHumpName}Entity.java', '#if(data.subModuleName != null && data.subModuleName != \"\")\npackage #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).entity;\n#else\npackage #(data.packageName+\".\"+data.moduleName).entity;\n#end\n\n#for(pkg : data.model.entityPkgList)\nimport #(pkg);\n#end\nimport com.baomidou.mybatisplus.annotation.FieldStrategy;\nimport com.baomidou.mybatisplus.annotation.TableField;\nimport com.baomidou.mybatisplus.annotation.TableLogic;\nimport lombok.Data;\nimport lombok.EqualsAndHashCode;\nimport org.opsli.core.base.entity.BaseEntity;\n\n/**\n * #(data.codeTitle) Entity\n *\n * <AUTHOR> * @date #(currTime)\n */\n@Data\n@EqualsAndHashCode(callSuper = false)\npublic class #(data.model.tableHumpName) extends BaseEntity {\n\n\n    #for(column : data.model.columnList)\n    ### 不等于 删除字段 和 不等于 租户字段放入上边\n    #if(column.fieldHumpName != \"deleted\" && column.fieldHumpName != \"tenantId\")\n    /** #(column.fieldComments) */\n    #if(!column.izNotNull)\n    @TableField(updateStrategy = FieldStrategy.ALWAYS)\n    #end\n    private #(column.javaType) #(column.fieldHumpName);\n\n    #end\n    #end\n\n    // ========================================\n\n    ### 专门处理 删除字段 和 租户字段\n    #for(column : data.model.columnList)\n    #if(column.fieldHumpName == \"deleted\")\n    /** 逻辑删除字段 */\n    @TableLogic\n    private Integer deleted;\n    #else if(column.fieldHumpName == \"tenantId\")\n    /** 多租户字段 */\n    private String tenantId;\n    #end\n\n    #end\n\n}', '1', 0, 1, '2021-12-12 14:40:01', 1, '2021-12-12 14:40:01');
INSERT INTO `gen_template_detail` VALUES (1469919953401061378, 1398253704724828162, '0', '${packageName}/${moduleName}/${subModuleName}/mapper', '${model.tableHumpName}Mapper.java', '#if(data.subModuleName != null && data.subModuleName != \"\")\npackage #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).mapper;\n#else\npackage #(data.packageName+\".\"+data.moduleName).mapper;\n#end\n\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\nimport org.apache.ibatis.annotations.Mapper;\nimport org.apache.ibatis.annotations.Param;\n#if(data.subModuleName != null && data.subModuleName != \"\")\nimport #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).entity.#(data.model.tableHumpName);\n#else\nimport #(data.packageName+\".\"+data.moduleName).entity.#(data.model.tableHumpName);\n#end\n\n/**\n * #(data.codeTitle) Mapper\n *\n * <AUTHOR> * @date #(currTime)\n */\n@Mapper\npublic interface #(data.model.tableHumpName)Mapper extends BaseMapper<#(data.model.tableHumpName)> {\n\n}', '0', 0, 1, '2021-12-12 14:40:01', 1, '2021-12-12 14:40:01');
INSERT INTO `gen_template_detail` VALUES (1469919953589805058, 1398253704724828162, '0', '${packageName}/${moduleName}/${subModuleName}/mapper/xml', '${model.tableHumpName}Mapper.xml', '<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n#if(data.subModuleName != null && data.subModuleName != \"\")\n<mapper namespace=\"#(data.packageName+\'.\'+data.moduleName+\'.\'+data.subModuleName).mapper.#(data.model.tableHumpName)Mapper\">\n#else\n<mapper namespace=\"#(data.packageName+\'.\'+data.moduleName).mapper.#(data.model.tableHumpName)Mapper\">\n#end\n\n\n</mapper>', '0', 0, 1, '2021-12-12 14:40:01', 1, '2021-12-12 14:40:01');
INSERT INTO `gen_template_detail` VALUES (1469919953728217090, 1398253704724828162, '0', 'org/opsli/api/wrapper/${moduleName}/${subModuleName}', '${model.tableHumpName}Model.java', '#if(data.subModuleName != null && data.subModuleName != \"\")\npackage #(apiPath).wrapper.#(data.moduleName+\".\"+data.subModuleName);\n#else\npackage #(apiPath).wrapper.#(data.moduleName);\n#end\n\n#for(pkg : data.model.entityPkgList)\nimport #(pkg);\n#end\nimport com.alibaba.excel.annotation.ExcelProperty;\nimport io.swagger.annotations.ApiModelProperty;\nimport lombok.Data;\nimport lombok.EqualsAndHashCode;\nimport #(apiPath).base.warpper.ApiWrapper;\nimport org.opsli.common.annotation.validator.Validator;\nimport org.opsli.common.annotation.validator.ValidatorLenMax;\nimport org.opsli.common.annotation.validator.ValidatorLenMin;\nimport org.opsli.common.enums.ValidatorType;\nimport org.opsli.plugins.excel.annotation.ExcelInfo;\nimport com.fasterxml.jackson.annotation.JsonFormat;\nimport org.springframework.format.annotation.DateTimeFormat;\n\n/**\n* #(data.codeTitle) Model\n*\n* <AUTHOR> @date #(currTime)\n*/\n@Data\n@EqualsAndHashCode(callSuper = false)\npublic class #(data.model.tableHumpName)Model extends ApiWrapper {\n\n    #for(column : data.model.columnList)\n    ### 不等于 删除字段 和 不等于 租户字段放入上边\n    #if(column.fieldHumpName != \"deleted\" && column.fieldHumpName != \"tenantId\")\n    /** #(column.fieldComments) */\n    @ApiModelProperty(value = \"#(column.fieldComments)\")\n    @ExcelProperty(value = \"#(column.fieldComments)\", order = #(column.sort))\n    #if(column.dictTypeCode != null && column.dictTypeCode != \"\")\n    @ExcelInfo( dictType = \"#(column.dictTypeCode)\" )\n    #else\n    @ExcelInfo\n    #end\n    #if(column.validateTypeAndCommaList != null && column.validateTypeAndCommaList.size() > 0)\n    @Validator({\n        #for(typeAndComma : column.validateTypeAndCommaList)\n        ValidatorType.#(typeAndComma)\n        #end\n    })\n    #end\n    #if(column.fieldLength != null && column.fieldLength > 0)\n    #if(column.fieldPrecision != null && column.fieldPrecision > 0)\n    @ValidatorLenMax(#(column.fieldLength+column.fieldPrecision))\n    #else\n    @ValidatorLenMax(#(column.fieldLength))\n    #end\n    #end\n    ### 日期处理\n    #if(column.javaType == \"Date\")\n    #if(column.showType == \"4\")\n    @JsonFormat(timezone = \"GMT+8\", pattern = \"yyyy-MM-dd\")\n    @DateTimeFormat(pattern = \"yyyy-MM-dd\")\n    #else\n    @JsonFormat(timezone = \"GMT+8\", pattern = \"yyyy-MM-dd HH:mm:ss\")\n    @DateTimeFormat(pattern = \"yyyy-MM-dd HH:mm:ss\")\n    #end\n    #end\n    private #(column.javaType) #(column.fieldHumpName);\n\n    #end\n    #end\n\n\n}', '0', 0, 1, '2021-12-12 14:40:01', 1, '2021-12-12 14:40:01');
INSERT INTO `gen_template_detail` VALUES (1469919953849851905, 1398253704724828162, '0', 'org/opsli/api/web/${moduleName}/${subModuleName}', '${model.tableHumpName}RestApi.java', '#if(data.subModuleName != null && data.subModuleName != \"\")\npackage #(apiPath).web.#(data.moduleName+\".\"+data.subModuleName);\n#else\npackage #(apiPath).web.#(data.moduleName);\n#end\n\nimport #(apiPath).base.result.ResultVo;\nimport org.springframework.web.bind.annotation.GetMapping;\nimport org.springframework.web.bind.annotation.PostMapping;\nimport org.springframework.web.bind.annotation.RequestBody;\nimport org.springframework.web.bind.annotation.RequestParam;\nimport org.springframework.web.multipart.MultipartHttpServletRequest;\nimport jakarta.servlet.http.HttpServletRequest;\nimport jakarta.servlet.http.HttpServletResponse;\n\n#if(data.subModuleName != null && data.subModuleName != \"\")\nimport #(apiPath).wrapper.#(data.moduleName+\".\"+data.subModuleName).#(data.model.tableHumpName)Model;\n#else\nimport #(apiPath).wrapper.#(data.moduleName).#(data.model.tableHumpName)Model;\n#end\n\n\n/**\n * #(data.codeTitle) Api\n *\n * 对外 API 直接 暴露 @GetMapping 或者 @PostMapping\n * 对内也推荐 单机版 不需要设置 Mapping 但是调用方法得从Controller写起\n *\n * 这样写法虽然比较绕，但是当单体项目想要改造微服务架构时 时非常容易的\n *\n * <AUTHOR> * @date #(currTime)\n */\npublic interface #(data.model.tableHumpName)RestApi {\n\n    /** 标题 */\n    String TITLE = \"#(data.codeTitle)\";\n    /** 子标题 */\n    String SUB_TITLE = \"#(data.codeTitleBrief)\";\n\n    /**\n    * #(data.codeTitle) 查一条\n    * @param model 模型\n    * @return ResultWrapper\n    */\n    @GetMapping(\"/get\")\n    ResultWrapper<#(data.model.tableHumpName)Model> get(#(data.model.tableHumpName)Model model);\n\n    /**\n    * #(data.codeTitle) 查询分页\n    * @param pageNo 当前页\n    * @param pageSize 每页条数\n    * @param request request\n    * @return ResultWrapper\n    */\n    @GetMapping(\"/findPage\")\n    ResultWrapper<?> findPage(\n        @RequestParam(name = \"pageNo\", defaultValue = \"1\") Integer pageNo,\n        @RequestParam(name = \"pageSize\", defaultValue = \"10\") Integer pageSize,\n        HttpServletRequest request\n    );\n\n    /**\n    * #(data.codeTitle) 新增\n    * @param model 模型\n    * @return ResultWrapper\n    */\n    @PostMapping(\"/insert\")\n    ResultWrapper<?> insert(@RequestBody #(data.model.tableHumpName)Model model);\n\n    /**\n    * #(data.codeTitle) 修改\n    * @param model 模型\n    * @return ResultWrapper\n    */\n    @PostMapping(\"/update\")\n    ResultWrapper<?> update(@RequestBody #(data.model.tableHumpName)Model model);\n\n    /**\n    * #(data.codeTitle) 删除\n    * @param id ID\n    * @return ResultWrapper\n    */\n    @PostMapping(\"/del\")\n    ResultWrapper<?> del(String id);\n\n    /**\n    * #(data.codeTitle) 批量删除\n    * @param ids ID 数组\n    * @return ResultWrapper\n    */\n    @PostMapping(\"/delAll\")\n    ResultWrapper<?> delAll(String ids);\n\n    /**\n    * #(data.codeTitle) Excel 导出\n    *\n    * 导出时，Token认证和方法权限认证 全部都由自定义完成\n    * 因为在 导出不成功时，需要推送错误信息，\n    * 前端直接走下载流，当失败时无法获得失败信息，即使前后端换一种方式后端推送二进制文件前端再次解析也是最少2倍的耗时\n    * ，且如果数据量过大，前端进行渲染时直接会把浏览器卡死\n    * 而直接开启socket接口推送显然是太过浪费资源了，所以目前采用Java最原始的手段\n    * response 推送 javascript代码 alert 提示报错信息\n    *\n    * @param request request\n    * @param response response\n    */\n    @GetMapping(\"/exportExcel\")\n    void exportExcel(HttpServletRequest request, HttpServletResponse response);\n\n    /**\n    * #(data.codeTitle) Excel 导入\n    * @param request 文件流 request\n    * @return ResultWrapper\n    */\n    @PostMapping(\"/importExcel\")\n    ResultWrapper<?> importExcel(MultipartHttpServletRequest request);\n\n    /**\n    * #(data.codeTitle) Excel 下载导入模版\n    * @param response response\n    */\n    @GetMapping(\"/importExcel/template\")\n    void importTemplate(HttpServletResponse response);\n\n}', '0', 0, 1, '2021-12-12 14:40:01', 1, '2021-12-12 14:40:01');
INSERT INTO `gen_template_detail` VALUES (1469919954038595585, 1398253704724828162, '0', '${packageName}/${moduleName}/${subModuleName}/web', '${model.tableHumpName}RestController.java', '#if(data.subModuleName != null && data.subModuleName != \"\")\npackage #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).web;\n#else\npackage #(data.packageName+\".\"+data.moduleName).web;\n#end\n\nimport cn.hutool.core.util.ReflectUtil;\nimport cn.hutool.core.convert.Convert;\nimport io.swagger.v3.oas.annotations.tags.Tag;\nimport io.swagger.annotations.ApiOperation;\nimport lombok.extern.slf4j.Slf4j;\nimport org.opsli.common.annotation.RequiresPermissionsCus;\nimport org.springframework.security.access.prepost.PreAuthorize;\nimport #(apiPath).base.result.ResultVo;\nimport org.opsli.common.annotation.ApiRestController;\nimport org.opsli.common.annotation.EnableLog;\nimport org.opsli.core.base.controller.BaseRestController;\nimport org.opsli.core.persistence.Page;\nimport org.opsli.core.persistence.querybuilder.QueryBuilder;\nimport org.opsli.core.persistence.querybuilder.WebQueryBuilder;\nimport org.springframework.web.multipart.MultipartHttpServletRequest;\nimport jakarta.servlet.http.HttpServletRequest;\nimport jakarta.servlet.http.HttpServletResponse;\nimport java.lang.reflect.Method;\n\n#if(data.subModuleName != null && data.subModuleName != \"\")\nimport #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).entity.#(data.model.tableHumpName);\nimport #(apiPath).wrapper.#(data.moduleName+\".\"+data.subModuleName).#(data.model.tableHumpName)Model;\nimport #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).service.I#(data.model.tableHumpName)Service;\nimport #(apiPath).web.#(data.moduleName+\".\"+data.subModuleName).#(data.model.tableHumpName)RestApi;\n#else\nimport #(data.packageName+\".\"+data.moduleName).entity.#(data.model.tableHumpName);\nimport #(apiPath).wrapper.#(data.moduleName).#(data.model.tableHumpName)Model;\nimport #(data.packageName+\".\"+data.moduleName).service.I#(data.model.tableHumpName)Service;\nimport #(apiPath).web.#(data.moduleName).#(data.model.tableHumpName)RestApi;\n#end\n\n/**\n * #(data.codeTitle) Controller\n *\n * <AUTHOR> * @date #(currTime)\n */\n@Tag(name = #(data.model.tableHumpName)RestApi.TITLE)\n@Slf4j\n#if(data.subModuleName != null && data.subModuleName != \"\")\n@ApiRestController(\"/{ver}/#(data.moduleName)/#(data.subModuleName)\")\n#else\n@ApiRestController(\"/{ver}/#(data.moduleName)\")\n#end\npublic class #(data.model.tableHumpName)RestController extends BaseRestController<#(data.model.tableHumpName), #(data.model.tableHumpName)Model, I#(data.model.tableHumpName)Service>\n    implements #(data.model.tableHumpName)RestApi {\n\n\n    /**\n    * #(data.codeTitleBrief) 查一条\n    * @param model 模型\n    * @return ResultWrapper\n    */\n    @ApiOperation(value = \"获得单条#(data.codeTitleBrief)\", notes = \"获得单条#(data.codeTitleBrief) - ID\")\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_select\")\n    #else\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_select\")\n    #end\n    @Override\n    public ResultWrapper<#(data.model.tableHumpName)Model> get(#(data.model.tableHumpName)Model model) {\n        // 如果系统内部调用 则直接查数据库\n        if(model != null && model.getIzApi() != null && model.getIzApi()){\n            model = IService.get(model);\n        }\n        return ResultVo.success(model);\n    }\n\n    /**\n    * #(data.codeTitleBrief) 查询分页\n    * @param pageNo 当前页\n    * @param pageSize 每页条数\n    * @param request request\n    * @return ResultWrapper\n    */\n    @ApiOperation(value = \"获得分页数据\", notes = \"获得分页数据 - 查询构造器\")\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_select\")\n    #else\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_select\")\n    #end\n    @Override\n    public ResultWrapper<?> findPage(Integer pageNo, Integer pageSize, HttpServletRequest request) {\n\n        QueryBuilder<#(data.model.tableHumpName)> queryBuilder = new WebQueryBuilder<>(IService.getEntityClass(), request.getParameterMap());\n        Page<#(data.model.tableHumpName), #(data.model.tableHumpName)Model> page = new Page<>(pageNo, pageSize);\n        page.setQueryWrapper(queryBuilder.build());\n        page = IService.findPage(page);\n\n        return ResultVo.success(page.getPageData());\n    }\n\n    /**\n    * #(data.codeTitleBrief) 新增\n    * @param model 模型\n    * @return ResultWrapper\n    */\n    @ApiOperation(value = \"新增#(data.codeTitleBrief)数据\", notes = \"新增#(data.codeTitleBrief)数据\")\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_insert\")\n    #else\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_insert\")\n    #end\n    @EnableLog\n    @Override\n    public ResultWrapper<?> insert(#(data.model.tableHumpName)Model model) {\n        // 调用新增方法\n        IService.insert(model);\n        return ResultVo.success(\"新增#(data.codeTitleBrief)成功\");\n    }\n\n    /**\n    * #(data.codeTitleBrief) 修改\n    * @param model 模型\n    * @return ResultWrapper\n    */\n    @ApiOperation(value = \"修改#(data.codeTitleBrief)数据\", notes = \"修改#(data.codeTitleBrief)数据\")\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_update\")\n    #else\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_update\")\n    #end\n    @EnableLog\n    @Override\n    public ResultWrapper<?> update(#(data.model.tableHumpName)Model model) {\n        // 调用修改方法\n        IService.update(model);\n        return ResultVo.success(\"修改#(data.codeTitleBrief)成功\");\n    }\n\n\n    /**\n    * #(data.codeTitleBrief) 删除\n    * @param id ID\n    * @return ResultWrapper\n    */\n    @ApiOperation(value = \"删除#(data.codeTitleBrief)数据\", notes = \"删除#(data.codeTitleBrief)数据\")\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_update\")\n    #else\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_update\")\n    #end\n    @EnableLog\n    @Override\n    public ResultWrapper<?> del(String id){\n        IService.delete(id);\n        return ResultVo.success(\"删除#(data.codeTitleBrief)成功\");\n    }\n\n    /**\n    * #(data.codeTitleBrief) 批量删除\n    * @param ids ID 数组\n    * @return ResultWrapper\n    */\n    @ApiOperation(value = \"批量删除#(data.codeTitleBrief)数据\", notes = \"批量删除#(data.codeTitleBrief)数据\")\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_update\")\n    #else\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_update\")\n    #end\n    @EnableLog\n    @Override\n    public ResultWrapper<?> delAll(String ids){\n        String[] idArray = Convert.toStrArray(ids);\n        IService.deleteAll(idArray);\n        return ResultVo.success(\"批量删除#(data.codeTitleBrief)成功\");\n    }\n\n\n    /**\n    * #(data.codeTitleBrief) Excel 导出\n    * 注：这里 RequiresPermissionsCus 引入的是 自定义鉴权注解\n    *\n    * 导出时，Token认证和方法权限认证 全部都由自定义完成\n    * 因为在 导出不成功时，需要推送错误信息，\n    * 前端直接走下载流，当失败时无法获得失败信息，即使前后端换一种方式后端推送二进制文件前端再次解析也是最少2倍的耗时\n    * ，且如果数据量过大，前端进行渲染时直接会把浏览器卡死\n    * 而直接开启socket接口推送显然是太过浪费资源了，所以目前采用Java最原始的手段\n    * response 推送 javascript代码 alert 提示报错信息\n    *\n    * @param request request\n    * @param response response\n    */\n    @ApiOperation(value = \"导出Excel\", notes = \"导出Excel\")\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    @RequiresPermissionsCus(\"#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_export\")\n    #else\n    @RequiresPermissionsCus(\"#(data.moduleName.toLowerCase())_export\")\n    #end\n    @EnableLog\n    @Override\n    public void exportExcel(HttpServletRequest request, HttpServletResponse response) {\n        // 当前方法\n        Method method = ReflectUtil.getMethodByName(this.getClass(), \"exportExcel\");\n        QueryBuilder<#(data.model.tableHumpName)> queryBuilder = new WebQueryBuilder<>(IService.getEntityClass(), request.getParameterMap());\n        super.excelExport(#(data.model.tableHumpName)RestApi.SUB_TITLE, queryBuilder.build(), response, method);\n    }\n\n    /**\n    * #(data.codeTitleBrief) Excel 导入\n    * 注：这里 RequiresPermissions 引入的是 Shiro原生鉴权注解\n    * @param request 文件流 request\n    * @return ResultWrapper\n    */\n    @ApiOperation(value = \"导入Excel\", notes = \"导入Excel\")\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_import\")\n    #else\n    @RequiresPermissions(\"#(data.moduleName.toLowerCase())_import\")\n    #end\n    @EnableLog\n    @Override\n    public ResultWrapper<?> importExcel(MultipartHttpServletRequest request) {\n        return super.importExcel(request);\n    }\n\n    /**\n    * #(data.codeTitleBrief) Excel 下载导入模版\n    * 注：这里 RequiresPermissionsCus 引入的是 自定义鉴权注解\n    * @param response response\n    */\n    @ApiOperation(value = \"导出Excel模版\", notes = \"导出Excel模版\")\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    @RequiresPermissionsCus(\"#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_import\")\n    #else\n    @RequiresPermissionsCus(\"#(data.moduleName.toLowerCase())_import\")\n    #end\n    @Override\n    public void importTemplate(HttpServletResponse response) {\n        // 当前方法\n        Method method = ReflectUtil.getMethodByName(this.getClass(), \"importTemplate\");\n        super.importTemplate(#(data.model.tableHumpName)RestApi.SUB_TITLE, response, method);\n    }\n\n}', '0', 0, 1, '2021-12-12 14:40:01', 1, '2021-12-12 14:40:01');
INSERT INTO `gen_template_detail` VALUES (1469919954235727874, 1398253704724828162, '0', '${packageName}/${moduleName}/${subModuleName}/service/impl', '${model.tableHumpName}ServiceImpl.java', '#if(data.subModuleName != null && data.subModuleName != \"\")\npackage #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).service.impl;\n#else\npackage #(data.packageName+\".\"+data.moduleName).service.impl;\n#end\n\n\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Service;\nimport org.springframework.transaction.annotation.Transactional;\nimport org.opsli.core.base.service.impl.CrudServiceImpl;\n\n#if(data.subModuleName != null && data.subModuleName != \"\")\nimport #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).entity.#(data.model.tableHumpName);\nimport #(apiPath).wrapper.#(data.moduleName+\".\"+data.subModuleName).#(data.model.tableHumpName)Model;\nimport #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).service.I#(data.model.tableHumpName)Service;\nimport #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).mapper.#(data.model.tableHumpName)Mapper;\n#else\nimport #(data.packageName+\".\"+data.moduleName).entity.#(data.model.tableHumpName);\nimport #(apiPath).wrapper.#(data.moduleName).#(data.model.tableHumpName)Model;\nimport #(data.packageName+\".\"+data.moduleName).service.I#(data.model.tableHumpName)Service;\nimport #(data.packageName+\".\"+data.moduleName).mapper.#(data.model.tableHumpName)Mapper;\n#end\n\n\n/**\n * #(data.codeTitle) Service Impl\n *\n * <AUTHOR> * @date #(currTime)\n */\n@Service\npublic class #(data.model.tableHumpName)ServiceImpl extends CrudServiceImpl<#(data.model.tableHumpName)Mapper, #(data.model.tableHumpName), #(data.model.tableHumpName)Model>\n    implements I#(data.model.tableHumpName)Service {\n\n    @Autowired(required = false)\n    private #(data.model.tableHumpName)Mapper mapper;\n\n}', '0', 0, 1, '2021-12-12 14:40:02', 1, '2021-12-12 14:40:02');
INSERT INTO `gen_template_detail` VALUES (1469919954428665858, 1398253704724828162, '0', '${packageName}/${moduleName}/${subModuleName}/service', 'I${model.tableHumpName}Service.java', '#if(data.subModuleName != null && data.subModuleName != \"\")\npackage #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).service;\n#else\npackage #(data.packageName+\".\"+data.moduleName).service;\n#end\n\nimport org.opsli.core.base.service.interfaces.CrudServiceInterface;\n\n\n#if(data.subModuleName != null && data.subModuleName != \"\")\nimport #(data.packageName+\".\"+data.moduleName+\".\"+data.subModuleName).entity.#(data.model.tableHumpName);\nimport #(apiPath).wrapper.#(data.moduleName+\".\"+data.subModuleName).#(data.model.tableHumpName)Model;\n#else\nimport #(data.packageName+\".\"+data.moduleName).entity.#(data.model.tableHumpName);\nimport #(apiPath).wrapper.#(data.moduleName).#(data.model.tableHumpName)Model;\n#end\n\n/**\n * #(data.codeTitle) Service\n *\n * <AUTHOR> * @date #(currTime)\n */\npublic interface I#(data.model.tableHumpName)Service extends CrudServiceInterface<#(data.model.tableHumpName), #(data.model.tableHumpName)Model> {\n\n}', '0', 0, 1, '2021-12-12 14:40:02', 1, '2021-12-12 14:40:02');
INSERT INTO `gen_template_detail` VALUES (1469919954567077890, 1398253704724828162, '1', 'src/api/${moduleName}/${subModuleName}', '${model.tableHumpName}ManagementApi.js', 'import request from \"@/utils/request\";\nimport { downloadFileByData } from \"@/utils/download\";\n\nexport function getList(data) {\n  return request({\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    url: \"/api/v1/#(data.moduleName)/#(data.subModuleName)/findPage\",\n    #else\n    url: \"/api/v1/#(data.moduleName)/findPage\",\n    #end\n    method: \"get\",\n    params: data,\n  });\n}\n\nexport function doInsert(data) {\n  return request({\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    url: \"/api/v1/#(data.moduleName)/#(data.subModuleName)/insert\",\n    #else\n    url: \"/api/v1/#(data.moduleName)/insert\",\n    #end\n    method: \"post\",\n    data,\n  });\n}\n\nexport function doUpdate(data) {\n  return request({\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    url: \"/api/v1/#(data.moduleName)/#(data.subModuleName)/update\",\n    #else\n    url: \"/api/v1/#(data.moduleName)/update\",\n    #end\n    method: \"post\",\n    data,\n  });\n}\n\nexport function doDelete(data) {\n  return request({\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    url: \"/api/v1/#(data.moduleName)/#(data.subModuleName)/del\",\n    #else\n    url: \"/api/v1/#(data.moduleName)/del\",\n    #end\n    method: \"post\",\n    params: data,\n  });\n}\n\nexport function doDeleteAll(data) {\n  return request({\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    url: \"/api/v1/#(data.moduleName)/#(data.subModuleName)/delAll\",\n    #else\n    url: \"/api/v1/#(data.moduleName)/delAll\",\n    #end\n    method: \"post\",\n    params: data,\n  });\n}\n\n/**\n * 导出Excel 目前只支持一层参数传递\n * @param data\n * @returns file\n */\nexport function doExportExcel(data) {\n  #if(data.subModuleName != null && data.subModuleName != \"\")\n  let requestURL = \"/api/v1/#(data.moduleName)/#(data.subModuleName)/exportExcel\";\n  #else\n  let requestURL = \"/api/v1/#(data.moduleName)/exportExcel\";\n  #end\n  // 下载文件\n  downloadFileByData(requestURL, data);\n}\n\n/**\n * 下载模版\n * @returns file\n */\nexport function doDownloadTemplate() {\n  let data = {};\n  #if(data.subModuleName != null && data.subModuleName != \"\")\n  let requestURL = \"/api/v1/#(data.moduleName)/#(data.subModuleName)/importExcel/template\";\n  #else\n  let requestURL = \"/api/v1/#(data.moduleName)/importExcel/template\";\n  #end\n  // 下载文件\n  downloadFileByData(requestURL, data);\n}\n\n/**\n * 导入Excel\n * @returns file\n */\nexport function doImportExcel(data) {\n  return request({\n    #if(data.subModuleName != null && data.subModuleName != \"\")\n    url: \"/api/v1/#(data.moduleName)/#(data.subModuleName)/importExcel\",\n    #else\n    url: \"/api/v1/#(data.moduleName)/importExcel\",\n    #end\n    method: \"post\",\n    // 最长超时时间 3 分钟\n    timeout: 180000,\n    headers: {\n      \"Content-Type\": \"multipart/form-data\"\n    },\n    data,\n  });\n}', '0', 0, 1, '2021-12-12 14:40:02', 1, '2021-12-12 14:40:02');
INSERT INTO `gen_template_detail` VALUES (1469919954760015874, 1398253704724828162, '1', 'src/views/modules/${moduleName}/${subModuleName}/components', '${model.tableHumpName}ManagementEdit.vue', '<template>\n  <el-dialog\n    :title=\"title\"\n    :visible.sync=\"dialogFormVisible\"\n    :close-on-click-modal=\"false\"\n    width=\"800px\"\n    @close=\"close\"\n  >\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"105px\">\n      <el-row :gutter=\"10\" >\n      #for(column : data.model.formList)\n        ### 文本框\n        #if(column.showType == \"0\")\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"12\" :xl=\"12\">\n          <el-form-item label=\"#(column.fieldComments)\" prop=\"#(column.fieldHumpName)\">\n            <el-input v-model=\"form.#(column.fieldHumpName)\" autocomplete=\"off\"></el-input>\n          </el-form-item>\n        </el-col>\n        ### 文本域\n        #else if(column.showType == \"1\")\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"12\" :xl=\"12\">\n          <el-form-item label=\"#(column.fieldComments)\" prop=\"#(column.fieldHumpName)\">\n            <el-input type=\"textarea\" v-model=\"form.#(column.fieldHumpName)\" autocomplete=\"off\"></el-input>\n          </el-form-item>\n        </el-col>\n        ### 字典\n        #else if(column.showType == \"2\")\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"12\" :xl=\"12\">\n          <el-form-item label=\"#(column.fieldComments)\" prop=\"#(column.fieldHumpName)\">\n            <el-select v-model=\"form.#(column.fieldHumpName)\" clearable\n                       placeholder=\"请选择\" style=\"width: 100%\">\n              <el-option\n                      v-for=\"item in dict.#(column.dictTypeCode)\"\n                      :key=\"item.dictValue\"\n                      :label=\"item.dictName\"\n                      :value=\"item.dictValue\"\n              ></el-option>\n            </el-select>\n          </el-form-item>\n        </el-col>\n        ### 日期时间\n        #else if(column.showType == \"3\")\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"12\" :xl=\"12\">\n          <el-form-item label=\"#(column.fieldComments)\" prop=\"#(column.fieldHumpName)\">\n            <el-date-picker\n                    v-model=\"form.#(column.fieldHumpName)\"\n                    type=\"datetime\"\n                    placeholder=\"选择#(column.fieldComments)\"\n                    style=\"width: 100%\"\n            ></el-date-picker>\n          </el-form-item>\n        </el-col>\n        ### 日期\n        #else if(column.showType == \"4\")\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"12\" :xl=\"12\">\n          <el-form-item label=\"#(column.fieldComments)\" prop=\"#(column.fieldHumpName)\">\n            <el-date-picker\n                    v-model=\"form.#(column.fieldHumpName)\"\n                    type=\"date\"\n                    placeholder=\"选择#(column.fieldComments)\"\n                    style=\"width: 100%\"\n            ></el-date-picker>\n          </el-form-item>\n        </el-col>\n        #end\n\n      #end\n      </el-row>\n\n    </el-form>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"close\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"save\">确 定</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\n  #if(data.subModuleName != null && data.subModuleName != \"\")\n  import { doInsert, doUpdate } from \"@/api/#(data.moduleName)/#(data.subModuleName)/#(data.model.tableHumpName)ManagementApi\";\n  #else\n  import { doInsert, doUpdate } from \"@/api/#(data.moduleName)/#(data.model.tableHumpName)ManagementApi\";\n  #end\n  import { isNull } from \"@/utils/validate\";\n  import { formateDate } from \"@/utils/format\";\n  import { validatorRule } from \"@/utils/validateRlue\";\n\n  export default {\n    name: \"#(data.model.tableHumpName)ManagementEdit\",\n    data() {\n\n      return {\n        form: {\n          // 设置默认值\n          version: 0\n        },\n        dict: {},\n        rules: {\n          #for(columnList : data.model.formList)\n          #for(column : columnList)\n            #if(column.validateTypeList != null && column.validateTypeList.size() > 0)\n              #(column.fieldHumpName): [\n                #for(typeNotComma : column.validateTypeList)\n                #if(typeNotComma == \"IS_NOT_NULL\")\n                { required: true, trigger: \"blur\", message: \"#(column.fieldComments)非空\" },\n                #end\n                #end\n                #for(typeNotComma : column.validateTypeList)\n                #if(typeNotComma != \"IS_NOT_NULL\")\n                { required: false, trigger: \"blur\", validator: validatorRule.#(typeNotComma) },\n                #end\n                #end\n              ],\n            #end\n          #end\n          #end\n        },\n        title: \"\",\n        dialogFormVisible: false,\n      };\n    },\n    created() {\n\n    },\n    mounted() {\n      // 加载字典值\n      #for(column : data.model.columnList)\n      #if(column.dictTypeCode != null && column.dictTypeCode != \"\")\n      this.dict.#(column.dictTypeCode) = this.$getDictList(\"#(column.dictTypeCode)\");\n      #end\n      #end\n    },\n    methods: {\n      showEdit(row) {\n        if (!row) {\n          this.title = \"添加\";\n        } else {\n          this.title = \"编辑\";\n          this.form = Object.assign({}, row);\n        }\n        this.dialogFormVisible = true;\n      },\n      close() {\n        this.dialogFormVisible = false;\n        this.$refs[\"form\"].resetFields();\n        this.form = this.$options.data().form;\n      },\n      save() {\n        this.$refs[\"form\"].validate(async (valid) => {\n          if (valid) {\n            // 处理数据\n            this.handlerFormData(this.form);\n\n            // 修改\n            if (!isNull(this.form.id)) {\n              const { success, msg } = await doUpdate(this.form);\n              if(success){\n                this.$baseMessage(msg, \"success\");\n              }\n            } else {\n              const { success, msg } = await doInsert(this.form);\n              if(success){\n                this.$baseMessage(msg, \"success\");\n              }\n            }\n\n            await this.$emit(\"fetchData\");\n            this.close();\n          } else {\n            return false;\n          }\n        });\n      },\n      // 处理 form数据\n      handlerFormData(formData){\n        if(!isNull(formData)){\n          for(let key in formData){\n            // 对于时间类进行处理\n            if(\"[object Date]\" === Object.prototype.toString.call(formData[key])){\n              formData[key] = formateDate(formData[key], \'yyyy-MM-dd hh:mm:ss\');\n            }\n          }\n        }\n      },\n    },\n  };\n</script>\n', '0', 0, 1, '2021-12-12 14:40:02', 1, '2021-12-12 14:40:02');
INSERT INTO `gen_template_detail` VALUES (1469919954948759553, 1398253704724828162, '1', 'src/views/modules/${moduleName}/${subModuleName}/components', '${model.tableHumpName}ManagementImport.vue', '<template>\n  <el-dialog\n    :title=\"title\"\n    :visible.sync=\"dialogFormVisible\"\n    :close-on-click-modal=\"false\"\n    width=\"800px\"\n    class=\"import-excel\"\n    @close=\"close\"\n  >\n    <el-upload\n      ref=\"excelImport\"\n      drag\n      accept=\".xls,.xlsx\"\n      style=\"width: 100%\"\n      :action=\"importExcelUrl\"\n      :multiple=\"false\"\n      :before-upload=\"beforeUpload\"\n      :http-request=\"handleImport\"\n      :on-success=\"onSuccess\"\n      :on-error=\"onError\"\n      :on-progress=\"onProcess\"\n    >\n      <i class=\"el-icon-upload\"></i>\n      <div class=\"el-upload__text\">将文件拖到此处，或<em>点击导入</em></div>\n      <div class=\"el-upload__tip\" slot=\"tip\">只能上传xls/xlsx文件，且不超过5MB</div>\n    </el-upload>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button type=\"primary\" @click=\"downloadExcelTemplate\">下载模版</el-button>\n      <el-button @click=\"close\">关 闭</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\n  #if(data.subModuleName != null && data.subModuleName != \"\")\n  import { doDownloadTemplate, doImportExcel } from \"@/api/#(data.moduleName)/#(data.subModuleName)/#(data.model.tableHumpName)ManagementApi\";\n  #else\n  import { doDownloadTemplate, doImportExcel } from \"@/api/#(data.moduleName)/#(data.model.tableHumpName)ManagementApi\";\n  #end\n  import {isNull} from \"@/utils/validate\";\n  import {random} from \"@/utils\";\n\n  export default {\n    name: \"#(data.model.tableHumpName)ManagementImport\",\n    data() {\n      return {\n        title: \"导入Excel\",\n        importExcelUrl: \'\',\n        dialogFormVisible: false,\n        loadProgress: 0, // 动态显示进度条\n        progressFlag: false, // 关闭进度条,\n        progressMap: {}\n      };\n    },\n    created() {},\n    mounted() {},\n    methods: {\n      show() {\n        this.dialogFormVisible = true;\n      },\n      close() {\n        this.dialogFormVisible = false;\n        this.$refs[\"excelImport\"].clearFiles();\n      },\n      // 下载模版\n      downloadExcelTemplate() {\n        doDownloadTemplate();\n      },\n      // 上传成功\n      onSuccess(response, file, fileList){\n        this.successProcess(file.uid);\n        this.$emit(\"fetchData\");\n      },\n      // 上传失败\n      onError(err, file, fileList){\n        this.errorProcess(file.uid);\n      },\n      // 进度条\n      onProcess(event, file, fileList) {\n        file.status = \'uploading\';\n        file.percentage = 0;\n        this.progressMap[file.uid] = {\n          file: file,\n        }\n        this.autoLoadingProcess(file.uid);\n      },\n\n      // 导入文件限制验证\n      beforeUpload(file) {\n        let testMsg = file.name.substring(file.name.lastIndexOf(\'.\')+1)\n        const extension = testMsg === \'xls\'\n        const extension2 = testMsg === \'xlsx\'\n        const isLt2M = file.size / 1024 / 1024 < 5\n        if(!extension && !extension2) {\n          this.$baseMessage(\'上传文件只能是 xls、xlsx格式!\', \"warning\");\n        }\n        if(!isLt2M) {\n          this.$baseMessage(\'上传文件大小不能超过 5MB!\', \"warning\");\n        }\n        return (extension || extension2) && isLt2M\n      },\n      // 自定义导入\n      handleImport(params){\n        if(!isNull(params)){\n          let blobObject = new Blob([params.file]);\n          let formData = new window.FormData()\n          formData.append(\"file\", blobObject);\n          const ret = doImportExcel(formData);\n          ret.then((v) => {\n            const {success,msg,data} = v;\n            if(success){\n              this.$baseMessage(msg, \"success\");\n              // 成功\n              params.onSuccess();\n            }else{\n              // 文件进度 100%\n              this.errorProcess(params.file.uid);\n              // 失败\n              params.onError();\n            }\n          }).catch( (e) =>{\n            // 失败\n            params.onError();\n          });\n          // 上传进度\n          params.onProgress();\n        }else{\n          params.onError();\n        }\n      },\n\n      // ==============\n\n      successProcess(fileUid) {\n        let tmp = this.progressMap[fileUid];\n        if(tmp !== null && tmp !== undefined){\n          try {\n            window.clearTimeout(tmp.timer);\n          }catch (e){}\n          tmp.file.status = \'success\';\n          tmp.file.percentage = 100;\n          delete this.progressMap[fileUid];\n        }\n      },\n      errorProcess(fileUid) {\n        let tmp = this.progressMap[fileUid];\n        if(tmp !== null && tmp !== undefined){\n          try {\n            window.clearTimeout(tmp.timer);\n          }catch (e){}\n          tmp.file.status = \'fail\';\n          delete this.progressMap[fileUid];\n        }\n      },\n      autoLoadingProcess(fileUid) {\n        const that = this;\n        let tmp = this.progressMap[fileUid];\n        if(tmp !== null && tmp !== undefined){\n          if(tmp.file.percentage >= 99) {\n            try {\n              window.clearTimeout(tmp.timer);\n            }catch (e){}\n          }else {\n            // 如果大于 99 则 停止\n            if(tmp.file.percentage + random(1, 12) > 99){\n              tmp.file.percentage = 99;\n            }else{\n              // 进度随机增长 1 - 12\n              tmp.file.percentage += random(1, 12);\n            }\n\n            // 递归增加百分比 递归时间为 随机 1-5秒\n            tmp.timer = window.setTimeout(function (){\n              that.autoLoadingProcess(fileUid);\n            }, random(1000, 5000));\n          }\n        }\n      },\n\n    },\n  };\n</script>\n', '0', 0, 1, '2021-12-12 14:40:02', 1, '2021-12-12 14:40:02');
INSERT INTO `gen_template_detail` VALUES (1469919955074588673, 1398253704724828162, '1', 'src/views/modules/${moduleName}/${subModuleName}', 'index.vue', '<template>\n  <div class=\"tenantManagement-container\">\n\n    <el-collapse-transition>\n    <div class=\"more-query\" v-show=\"this.moreQueryFlag\">\n      <!-- 更多查找 -->\n      <vab-query-form>\n        <vab-query-form-left-panel :span=\"24\">\n          <el-form :inline=\"true\" :model=\"queryForm\" @submit.native.prevent>\n            #for(column : data.model.moreQueryList)\n\n            ### 字典\n            #if(column.showType == \"2\")\n            <el-form-item>\n              <el-select v-model=\"queryForm.#(column.fieldHumpName+\'_\'+column.queryType)\" placeholder=\"请选择#(column.fieldComments)\" clearable style=\"width: 100%\">\n                <el-option\n                      v-for=\"item in dict.#(column.dictTypeCode)\"\n                      :key=\"item.dictValue\"\n                      :label=\"item.dictName\"\n                      :value=\"item.dictValue\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n            #else if(column.showType == \"3\")\n            ### 时间\n            <el-form-item>\n              <el-date-picker\n                      v-model=\"#(column.fieldHumpName)DatePicker\"\n                      type=\"datetimerange\"\n                      :picker-options=\"pickerOptions\"\n                      range-separator=\"至\"\n                      start-placeholder=\"开始#(column.fieldComments)\"\n                      end-placeholder=\"结束#(column.fieldComments)\"\n                      align=\"right\">\n              </el-date-picker>\n            </el-form-item>\n            #else if(column.showType == \"4\")\n            ### 日期\n            <el-form-item>\n              <el-date-picker\n                      v-model=\"#(column.fieldHumpName)DatePicker\"\n                      type=\"daterange\"\n                      align=\"right\"\n                      range-separator=\"至\"\n                      start-placeholder=\"开始#(column.fieldComments)\"\n                      end-placeholder=\"结束#(column.fieldComments)\"\n              ></el-date-picker>\n            </el-form-item>\n            #else\n            #if(column.queryType == \"EQ\" || column.queryType == \"LIKE\")\n            <el-form-item>\n              <el-input\n                      v-model.trim=\"queryForm.#(column.fieldHumpName)_#(column.queryType)\"\n                      placeholder=\"请输入#(column.fieldComments)\"\n                      clearable\n              />\n            </el-form-item>\n            #else if(column.queryType == \"RANGE\")\n            <el-col :span=\"12\" >\n            <el-form-item style=\"text-align: center\">\n              <el-input\n                      v-model.trim=\"queryForm.#(column.fieldHumpName)_BEGIN\"\n                      placeholder=\"#(column.fieldComments)开始\"\n                      clearable\n                      style=\"float: left;width: calc(50% - 6px)\"\n              />\n              <div style=\"float:left;width: 12px\">-</div>\n              <el-input\n                      v-model.trim=\"queryForm.#(column.fieldHumpName)_END\"\n                      placeholder=\"#(column.fieldComments)结束\"\n                      clearable\n                      style=\"float: right;width: calc(50% - 6px)\"\n              />\n            </el-form-item>\n            </el-col>\n            #end\n            #end\n            #end\n\n\n          </el-form>\n        </vab-query-form-left-panel>\n\n      </vab-query-form>\n      <el-divider></el-divider>\n    </div>\n    </el-collapse-transition>\n\n    <!-- 主要操作  -->\n    <vab-query-form>\n      <vab-query-form-left-panel :span=\"10\">\n        <el-button\n            #if(data.subModuleName != null && data.subModuleName != \"\")\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_insert\')\"\n            #else\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_insert\')\"\n            #end\n            icon=\"el-icon-plus\"\n            type=\"primary\"\n            @click=\"handleInsert\"\n        > 添加 </el-button>\n\n        <el-button\n            #if(data.subModuleName != null && data.subModuleName != \"\")\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_import\')\"\n            #else\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_import\')\"\n            #end\n            icon=\"el-icon-upload2\"\n            type=\"warning\"\n            @click=\"handleImportExcel\"\n        > 导入 </el-button>\n\n        <el-button\n            #if(data.subModuleName != null && data.subModuleName != \"\")\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_export\')\"\n            #else\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_export\')\"\n            #end\n            icon=\"el-icon-download\"\n            type=\"warning\"\n            @click=\"handleExportExcel\"\n        > 导出 </el-button>\n\n        <el-button\n            #if(data.subModuleName != null && data.subModuleName != \"\")\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_delete\')\"\n            #else\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_delete\')\"\n            #end\n            :disabled=\"!selectRows.length > 0\"\n            icon=\"el-icon-delete\"\n            type=\"danger\"\n            @click=\"handleDelete\"\n        > 批量删除 </el-button>\n\n      </vab-query-form-left-panel>\n      <vab-query-form-right-panel :span=\"14\">\n        <el-form :inline=\"true\" :model=\"queryForm\" @submit.native.prevent>\n          ### 代码生成器 简要只展示2个\n          #for(column : data.model.briefQueryList)\n\n          ### 字典\n          #if(column.showType == \"2\")\n          <el-form-item>\n            <el-select v-model=\"queryForm.#(column.fieldHumpName+\'_\'+column.queryType)\" placeholder=\"请选择#(column.fieldComments)\" clearable style=\"width: 100%\">\n              <el-option\n                      v-for=\"item in dict.#(column.dictTypeCode)\"\n                      :key=\"item.dictValue\"\n                      :label=\"item.dictName\"\n                      :value=\"item.dictValue\"\n              ></el-option>\n            </el-select>\n          </el-form-item>\n          #else if(column.showType == \"3\")\n          ### 时间\n          <el-form-item>\n            <el-date-picker\n                    v-model=\"#(column.fieldHumpName)DatePicker\"\n                    type=\"datetimerange\"\n                    :picker-options=\"pickerOptions\"\n                    range-separator=\"至\"\n                    start-placeholder=\"开始#(column.fieldComments)\"\n                    end-placeholder=\"结束#(column.fieldComments)\"\n                    align=\"right\">\n            </el-date-picker>\n          </el-form-item>\n          #else if(column.showType == \"4\")\n          ### 日期\n          <el-form-item>\n            <el-date-picker\n                    v-model=\"#(column.fieldHumpName)DatePicker\"\n                    type=\"daterange\"\n                    align=\"right\"\n                    range-separator=\"至\"\n                    start-placeholder=\"开始#(column.fieldComments)\"\n                    end-placeholder=\"结束#(column.fieldComments)\"\n            ></el-date-picker>\n          </el-form-item>\n          #else\n          #if(column.queryType == \"EQ\" || column.queryType == \"LIKE\")\n          <el-form-item>\n            <el-input\n                    v-model.trim=\"queryForm.#(column.fieldHumpName)_#(column.queryType)\"\n                    placeholder=\"请输入#(column.fieldComments)\"\n                    clearable\n            />\n          </el-form-item>\n          #else if(column.queryType == \"RANGE\")\n          <el-col :span=\"12\" >\n          <el-form-item style=\"text-align: center\">\n            <el-input\n                    v-model.trim=\"queryForm.#(column.fieldHumpName)_BEGIN\"\n                    placeholder=\"#(column.fieldComments)开始\"\n                    clearable\n                    style=\"float: left;width: calc(50% - 6px)\"\n            />\n            <div style=\"float:left;width: 12px\">-</div>\n            <el-input\n                    v-model.trim=\"queryForm.#(column.fieldHumpName)_END\"\n                    placeholder=\"#(column.fieldComments)结束\"\n                    clearable\n                    style=\"float: right;width: calc(50% - 6px)\"\n            />\n          </el-form-item>\n          </el-col>\n          #end\n          #end\n          #end\n\n          <el-form-item>\n            <el-button icon=\"el-icon-search\" type=\"primary\" @click=\"queryData\">\n              查询\n            </el-button>\n\n            #if(data.model.moreQueryList != null && data.model.moreQueryList.size() > 0)\n            <el-button icon=\"el-icon-search\" @click=\"moreQuery\">\n              更多\n            </el-button>\n            #end\n\n          </el-form-item>\n        </el-form>\n      </vab-query-form-right-panel>\n    </vab-query-form>\n\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"list\"\n      :element-loading-text=\"elementLoadingText\"\n      @selection-change=\"setSelectRows\"\n    >\n      <el-table-column show-overflow-tooltip type=\"selection\"></el-table-column>\n\n      <el-table-column show-overflow-tooltip label=\"序号\" width=\"95\">\n        <template slot-scope=\"scope\">\n          {{(queryForm.pageNo - 1) * queryForm.pageSize + scope.$index + 1}}\n        </template>\n      </el-table-column>\n\n      #for(column : data.model.columnList)\n      ### 字典\n      #if(column.showType == \"2\" && column.izShowList == \"1\")\n      <el-table-column\n              show-overflow-tooltip\n              prop=\"#(column.fieldHumpName)\"\n              label=\"#(column.fieldComments)\"\n      >\n\n        <template slot-scope=\"scope\">\n          <span>\n            {{ $getDictNameByValue(\'#(column.dictTypeCode)\', scope.row.#(column.fieldHumpName)) }}\n          </span>\n        </template>\n\n      </el-table-column>\n\n      #else\n      #if(column.izShowList == \"1\")\n      <el-table-column\n              show-overflow-tooltip\n              prop=\"#(column.fieldHumpName)\"\n              label=\"#(column.fieldComments)\"\n      ></el-table-column>\n\n      #end\n      #end\n      #end\n\n      <el-table-column\n        show-overflow-tooltip\n        label=\"操作\"\n        width=\"200\"\n        #if(data.subModuleName != null && data.subModuleName != \"\")\n        v-if=\"$perms(\'#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_update\') || $perms(\'#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_delete\')\"\n        #else\n        v-if=\"$perms(\'#(data.moduleName.toLowerCase())_update\') || $perms(\'#(data.moduleName.toLowerCase())_delete\')\"\n        #end\n      >\n        <template v-slot=\"scope\">\n          <el-button\n            #if(data.subModuleName != null && data.subModuleName != \"\")\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_update\')\"\n            #else\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_update\')\"\n            #end\n            type=\"text\"\n            @click=\"handleUpdate(scope.row)\"\n          > 编辑 </el-button>\n          \n          <el-divider direction=\"vertical\"></el-divider>\n          \n          <el-button\n            #if(data.subModuleName != null && data.subModuleName != \"\")\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_#(data.subModuleName.toLowerCase())_delete\')\"\n            #else\n            v-if=\"$perms(\'#(data.moduleName.toLowerCase())_delete\')\"\n            #end\n            type=\"text\"\n            @click=\"handleDelete(scope.row)\"\n          > 删除 </el-button>\n        </template>\n\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      background\n      :current-page=\"queryForm.pageNo\"\n      :page-size=\"queryForm.pageSize\"\n      :layout=\"layout\"\n      :total=\"total\"\n      @size-change=\"handleSizeChange\"\n      @current-change=\"handleCurrentChange\"\n    ></el-pagination>\n\n    <edit ref=\"edit\" @fetchData=\"fetchData\"></edit>\n    <import ref=\"import\" @fetchData=\"fetchData\" ></import>\n\n  </div>\n</template>\n\n<script>\n  #if(data.subModuleName != null && data.subModuleName != \"\")\n  import { getList, doDelete, doDeleteAll, doExportExcel } from \"@/api/#(data.moduleName)/#(data.subModuleName)/#(data.model.tableHumpName)ManagementApi\";\n  #else\n  import { getList, doDelete, doDeleteAll, doExportExcel } from \"@/api/#(data.moduleName)/#(data.model.tableHumpName)ManagementApi\";\n  #end\n  import Edit from \"./components/#(data.model.tableHumpName)ManagementEdit\";\n  import Import from \"./components/#(data.model.tableHumpName)ManagementImport\";\n\n  import { vueButtonClickBan } from \"@/utils\";\n  import { isNotNull } from \"@/utils/valiargs\";\n  import { formateDate } from \"@/utils/format\";\n\n  export default {\n    name: \"#(data.model.tableHumpName)Management\",\n    components: { Edit, Import },\n    data() {\n      return {\n        list: null,\n        listLoading: true,\n        layout: \"total, prev, pager, next, sizes, jumper\",\n        total: 0,\n        selectRows: \"\",\n        elementLoadingText: \"正在加载...\",\n        moreQueryFlag: false,\n        queryForm: {\n          pageNo: 1,\n          pageSize: 10,\n          ### 代码生成器 简要2个\n          #for(column : data.model.briefQueryList)\n          ### 字典\n          #if(column.showType == \"2\")\n          #(column.fieldHumpName)_EQ: \"\",\n          #else if(column.showType == \"3\" || column.showType == \"4\")\n          ### 日期\n          #(column.fieldHumpName)_BEGIN: \"\",\n          #(column.fieldHumpName)_END: \"\",\n          #else\n          #if(column.queryType == \"EQ\" || column.queryType == \"LIKE\")\n          #(column.fieldHumpName)_#(column.queryType): \"\",\n          #else if(column.queryType == \"RANGE\")\n          #(column.fieldHumpName)_BEGIN: \"\",\n          #(column.fieldHumpName)_END: \"\",\n          #end\n          #end\n          #end\n          ### 代码生成器 更多\n          #for(column : data.model.moreQueryList)\n          ### 字典\n          #if(column.showType == \"2\")\n          #(column.fieldHumpName)_EQ: \"\",\n          #else if(column.showType == \"3\" || column.showType == \"4\")\n          ### 日期\n          #(column.fieldHumpName)_BEGIN: \"\",\n          #(column.fieldHumpName)_END: \"\",\n          #else\n          #if(column.queryType == \"EQ\" || column.queryType == \"LIKE\")\n          #(column.fieldHumpName)_#(column.queryType): \"\",\n          #else if(column.queryType == \"RANGE\")\n          #(column.fieldHumpName)_BEGIN: \"\",\n          #(column.fieldHumpName)_END: \"\",\n          #end\n          #end\n          #end\n        },\n        ### 代码生成器 简要2个\n        #for(column : data.model.briefQueryList)\n        ### 日期\n        #if(column.showType == \"3\" || column.showType == \"4\")\n        #(column.fieldHumpName)DatePicker: [],\n        #end\n        #end\n        ### 代码生成器 更多\n        #for(column : data.model.moreQueryList)\n        ### 日期\n        #if(column.showType == \"3\" || column.showType == \"4\")\n        #(column.fieldHumpName)DatePicker: [],\n        #end\n        #end\n        dict:{},\n        pickerOptions: {\n          shortcuts: [{\n            text: \'最近一周\',\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n              picker.$emit(\'pick\', [start, end]);\n            }\n          }, {\n            text: \'最近一个月\',\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n              picker.$emit(\'pick\', [start, end]);\n            }\n          }, {\n            text: \'最近三个月\',\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n              picker.$emit(\'pick\', [start, end]);\n            }\n          }]\n        },\n      };\n    },\n    created() {\n      this.fetchData();\n    },\n    mounted() {\n      #for(column : data.model.columnList)\n      #if(column.dictTypeCode != null && column.dictTypeCode != \"\")\n      this.dict.#(column.dictTypeCode) = this.$getDictList(\"#(column.dictTypeCode)\");\n      #end\n      #end\n    },\n    methods: {\n      setSelectRows(val) {\n        this.selectRows = val;\n      },\n      handleInsert(row) {\n        this.$refs[\"edit\"].showEdit();\n      },\n      handleUpdate(row) {\n        if (row.id) {\n          this.$refs[\"edit\"].showEdit(row);\n        }\n      },\n      handleDelete(row) {\n        if (row.id) {\n          this.$baseConfirm(\"你确定要删除当前项吗\", null, async () => {\n            const { msg } = await doDelete({ id: row.id });\n            this.$baseMessage(msg, \"success\");\n            await this.fetchData();\n          });\n        } else {\n          if (this.selectRows.length > 0) {\n            const ids = this.selectRows.map((item) => item.id).join();\n            this.$baseConfirm(\"你确定要删除选中项吗\", null, async () => {\n              const { msg } = await doDeleteAll({ ids });\n              this.$baseMessage(msg, \"success\");\n              await this.fetchData();\n            });\n          } else {\n            this.$baseMessage(\"未选中任何行\", \"error\");\n            return false;\n          }\n        }\n      },\n      // 导出excel\n      handleExportExcel(el){\n        // 导出按钮防抖处理 默认限制为10秒\n        vueButtonClickBan(el, 10);\n\n        // 执行导出\n        doExportExcel(this.queryForm);\n      },\n      // 导入excel\n      handleImportExcel(){\n        this.$refs[\"import\"].show();\n      },\n\n\n      handleSizeChange(val) {\n        this.queryForm.pageSize = val;\n        this.fetchData();\n      },\n      handleCurrentChange(val) {\n        this.queryForm.pageNo = val;\n        this.fetchData();\n      },\n      moreQuery(){\n        this.moreQueryFlag = !this.moreQueryFlag;\n      },\n      queryData() {\n        ### 代码生成器 简要2个\n        #for(column : data.model.briefQueryList)\n        ### 日期\n        #if(column.showType == \"3\" || column.showType == \"4\")\n        if(isNotNull(this.#(column.fieldHumpName)DatePicker) && this.#(column.fieldHumpName)DatePicker.length === 2){\n          this.queryForm.#(column.fieldHumpName)_BEGIN =\n                  this.#(column.fieldHumpName)DatePicker.length === 0 ? \"\" : formateDate(this.#(column.fieldHumpName)DatePicker[0], \'yyyy-MM-dd hh:mm:ss\');\n          this.queryForm.#(column.fieldHumpName)_END =\n                  this.#(column.fieldHumpName)DatePicker.length === 0 ? \"\" : formateDate(this.#(column.fieldHumpName)DatePicker[1], \'yyyy-MM-dd hh:mm:ss\');\n        }else{\n          this.queryForm.#(column.fieldHumpName)_BEGIN = \"\";\n          this.queryForm.#(column.fieldHumpName)_END = \"\";\n        }        #end\n        #end\n        ### 代码生成器 更多\n        #for(column : data.model.moreQueryList)\n        ### 日期\n        #if(column.showType == \"3\" || column.showType == \"4\")\n        if(isNotNull(this.#(column.fieldHumpName)DatePicker) && this.#(column.fieldHumpName)DatePicker.length === 2){\n          this.queryForm.#(column.fieldHumpName)_BEGIN =\n                  this.#(column.fieldHumpName)DatePicker.length === 0 ? \"\" : formateDate(this.#(column.fieldHumpName)DatePicker[0], \'yyyy-MM-dd hh:mm:ss\');\n          this.queryForm.#(column.fieldHumpName)_END =\n                  this.#(column.fieldHumpName)DatePicker.length === 0 ? \"\" : formateDate(this.#(column.fieldHumpName)DatePicker[1], \'yyyy-MM-dd hh:mm:ss\');\n        }else{\n          this.queryForm.#(column.fieldHumpName)_BEGIN = \"\";\n          this.queryForm.#(column.fieldHumpName)_END = \"\";\n        }\n        #end\n        #end\n\n        this.queryForm.pageNo = 1;\n        this.fetchData();\n      },\n      async fetchData() {\n        this.listLoading = true;\n        const { data } = await getList(this.queryForm);\n        if(isNotNull(data)){\n          this.list = data.rows;\n          this.total = data.total;\n        }\n        setTimeout(() => {\n            this.listLoading = false;\n        }, 300);\n      },\n    },\n  };\n</script>\n', '0', 0, 1, '2021-12-12 14:40:02', 1, '2021-12-12 14:40:02');



-- 新增登录日志表
CREATE TABLE `sys_login_logs` (
              `id` bigint(19) NOT NULL COMMENT '唯一主键',
              `org_ids` varchar(500) DEFAULT '0' COMMENT '父级主键集合',
              `type` varchar(1) NOT NULL DEFAULT '1' COMMENT '日志类型 1登录日志 2退出日志',
              `remote_addr` varchar(255) DEFAULT NULL COMMENT '操作IP地址',
              `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
              `username` varchar(32) NOT NULL COMMENT '登录账户',
              `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
              `tenant_id` bigint(19) DEFAULT NULL COMMENT '多租户ID',
              `version` int(11) NOT NULL COMMENT '版本',
              `create_by` bigint(19) NOT NULL COMMENT '创建者',
              `create_time` datetime NOT NULL COMMENT '创建时间',
              `update_by` bigint(19) NOT NULL COMMENT '修改人',
              `update_time` datetime NOT NULL COMMENT '修改时间',
              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='登录信息表';

-- 操作日志表 新增多租户 与 组织机构字段
ALTER TABLE sys_logs
    ADD COLUMN `org_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '父级主键集合' AFTER `ts`,
    ADD COLUMN `tenant_id` bigint(19) NULL DEFAULT NULL COMMENT '多租户ID' AFTER `org_ids`;


-- 菜单相关
DELETE FROM sys_menu WHERE id = 1314782733087555586;
INSERT INTO `sys_menu`(`id`, `parent_id`, `parent_ids`, `menu_name`, `permissions`, `icon`, `label`, `type`, `url`, `component`, `redirect`, `sort_no`, `always_show`, `hidden`, `deleted`, `version`, `create_by`, `create_time`, `update_by`, `update_time`, `ts`) VALUES (1504780214448308226, 1504779965155655682, '0', '查看', 'devops_login_logs_select', NULL, '0,1', '2', NULL, NULL, NULL, 1, '0', '0', '0', 0, 1, '2022-03-18 19:22:15', 1, '2022-03-18 19:22:15', '2022-03-18 19:22:26');
INSERT INTO `sys_menu`(`id`, `parent_id`, `parent_ids`, `menu_name`, `permissions`, `icon`, `label`, `type`, `url`, `component`, `redirect`, `sort_no`, `always_show`, `hidden`, `deleted`, `version`, `create_by`, `create_time`, `update_by`, `update_time`, `ts`) VALUES (1504779965155655682, 1504776412970254338, '0', '登录日志', NULL, '', '0,1', '1', 'login-logs', 'views/modules/system/loginLogsManagement/index', NULL, 1, '0', '0', '0', 1, 1, '2022-03-18 19:21:16', 1, '2022-03-18 19:21:40', '2022-03-18 19:21:51');
INSERT INTO `sys_menu`(`id`, `parent_id`, `parent_ids`, `menu_name`, `permissions`, `icon`, `label`, `type`, `url`, `component`, `redirect`, `sort_no`, `always_show`, `hidden`, `deleted`, `version`, `create_by`, `create_time`, `update_by`, `update_time`, `ts`) VALUES (1504776412970254338, 0, '0', '日志监控', NULL, 'file-contract', '0,1', '1', '/log', 'Layout', NULL, 98, '0', '0', '0', 1, 1, '2022-03-18 19:07:09', 1, '2022-03-18 19:07:22', '2022-03-18 19:07:33');
UPDATE `sys_menu` SET `parent_id` = 1314616518671085570, `parent_ids` = '0,1504776412970254338,1314616518671085570', `menu_name` = '查看', `permissions` = 'devops_logs_select', `icon` = NULL, `label` = '0,1', `type` = '2', `url` = NULL, `component` = NULL, `redirect` = NULL, `sort_no` = 1, `always_show` = '0', `hidden` = '0', `deleted` = '0', `version` = 0, `create_by` = 1, `create_time` = '2020-10-09 23:19:53', `update_by` = 1, `update_time` = '2022-03-18 19:07:47', `ts` = '2022-03-18 19:07:58' WHERE `id` = 1314782679522099201;
UPDATE `sys_menu` SET `parent_id` = 1504776412970254338, `parent_ids` = '0,1504776412970254338,1314616518671085570', `menu_name` = '操作日志', `permissions` = NULL, `icon` = NULL, `label` = '0,1', `type` = '1', `url` = 'logs', `component` = 'views/modules/system/logsManagement/index', `redirect` = NULL, `sort_no` = 2, `always_show` = '0', `hidden` = '0', `deleted` = '0', `version` = 2, `create_by` = 1, `create_time` = '2020-10-09 12:19:37', `update_by` = 1, `update_time` = '2022-03-18 19:07:47', `ts` = '2022-03-18 19:07:58' WHERE `id` = 1314616518671085570;

-- 角色与菜单
DELETE FROM sys_role_menu_ref
WHERE menu_id IN (1314782733087555586, 1504780214448308226, 1504779965155655682, 1504776412970254338, 1314616518671085570, 1504776412970254338);
INSERT INTO `sys_role_menu_ref`(`id`, `menu_id`, `role_id`) VALUES (1505730836832075814, 1314616518671085570, 2);
INSERT INTO `sys_role_menu_ref`(`id`, `menu_id`, `role_id`) VALUES (1504780316210511897, 1314616518671085570, 1463431580473810945);
INSERT INTO `sys_role_menu_ref`(`id`, `menu_id`, `role_id`) VALUES (1505730836832075811, 1504776412970254338, 2);
INSERT INTO `sys_role_menu_ref`(`id`, `menu_id`, `role_id`) VALUES (1504780316210511894, 1504776412970254338, 1463431580473810945);
INSERT INTO `sys_role_menu_ref`(`id`, `menu_id`, `role_id`) VALUES (1505730836832075812, 1504779965155655682, 2);
INSERT INTO `sys_role_menu_ref`(`id`, `menu_id`, `role_id`) VALUES (1504780316210511895, 1504779965155655682, 1463431580473810945);
INSERT INTO `sys_role_menu_ref`(`id`, `menu_id`, `role_id`) VALUES (1505730836832075813, 1504780214448308226, 2);
INSERT INTO `sys_role_menu_ref`(`id`, `menu_id`, `role_id`) VALUES (1504780316210511896, 1504780214448308226, 1463431580473810945);


SET FOREIGN_KEY_CHECKS = 1;
