/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.attendance.service.impl;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opsli.api.wrapper.system.attendance.AttendanceModel;

import org.opsli.core.base.service.impl.CrudServiceImpl;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.modulars.system.attendance.entity.AttendanceMonthly;
import org.opsli.modulars.system.attendance.mapper.AttendanceMapper;
import org.opsli.modulars.system.attendance.service.IAttendanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 考勤管理 - 按月份 Service 实现类
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
@Service
public class AttendanceServiceImpl extends CrudServiceImpl<AttendanceMapper, AttendanceMonthly, AttendanceModel>
        implements IAttendanceService {

    @Autowired(required = false)
    private AttendanceMapper mapper;

    @Override
    public boolean checkAttendanceExists(AttendanceModel model) {
        if (model == null) {
            return false;
        }
        
        Long id = null;
        if (StringUtils.isNotBlank(model.getId())) {
            id = Long.valueOf(model.getId());
        }
        
        int count = mapper.checkAttendanceExists(
                model.getTenantId(), 
                model.getDataMonth(), 
                model.getEmployeeId(), 
                model.getAttendanceDate(), 
                id);
        
        return count == 0;
    }

    @Override
    public List<AttendanceModel> findByEmployeeId(Long tenantId, Date dataMonth, Long employeeId) {
        List<AttendanceMonthly> entityList = mapper.findByEmployeeId(tenantId, dataMonth, employeeId);
        return WrapperUtil.transformInstance(entityList, AttendanceModel.class);
    }

    @Override
    public List<AttendanceModel> findByDepartmentId(Long tenantId, Date dataMonth, Long departmentId) {
        List<AttendanceMonthly> entityList = mapper.findByDepartmentId(tenantId, dataMonth, departmentId);
        return WrapperUtil.transformInstance(entityList, AttendanceModel.class);
    }

    @Override
    public List<AttendanceModel> findByStatus(Long tenantId, Date dataMonth, String status) {
        List<AttendanceMonthly> entityList = mapper.findByStatus(tenantId, dataMonth, status);
        return WrapperUtil.transformInstance(entityList, AttendanceModel.class);
    }

    @Override
    public List<AttendanceModel> findByDateRange(Long tenantId, Date dataMonth, LocalDate startDate, LocalDate endDate) {
        List<AttendanceMonthly> entityList = mapper.findByDateRange(tenantId, dataMonth, startDate, endDate);
        return WrapperUtil.transformInstance(entityList, AttendanceModel.class);
    }

    @Override
    public int countByStatus(Long tenantId, Date dataMonth, String status) {
        return mapper.countByStatus(tenantId, dataMonth, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AttendanceModel insert(AttendanceModel model) {
        if (model == null) {
            return null;
        }

        // 检查考勤记录是否已存在
        if (!checkAttendanceExists(model)) {
            log.warn("该员工在此日期的考勤记录已存在: employeeId={}, attendanceDate={}",
                    model.getEmployeeId(), model.getAttendanceDate());
            return null;
        }

        return super.insert(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AttendanceModel update(AttendanceModel model) {
        if (model == null) {
            return null;
        }

        // 检查考勤记录是否已存在
        if (!checkAttendanceExists(model)) {
            log.warn("该员工在此日期的考勤记录已存在: employeeId={}, attendanceDate={}",
                    model.getEmployeeId(), model.getAttendanceDate());
            return null;
        }

        return super.update(model);
    }
}
