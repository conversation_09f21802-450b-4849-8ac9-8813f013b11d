/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.department.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.opsli.modulars.system.department.entity.DepartmentMonthly;

import java.util.Date;
import java.util.List;

/**
 * 部门管理 - 按月份 Mapper
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface DepartmentMapper extends BaseMapper<DepartmentMonthly> {

    /**
     * 根据父部门ID查询子部门列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<DepartmentMonthly> findByParentId(@Param("tenantId") Long tenantId, 
                                          @Param("dataMonth") Date dataMonth, 
                                          @Param("parentId") Long parentId);

    /**
     * 检查部门名称是否唯一（在同一租户和月份下）
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param name 部门名称
     * @param id 排除的部门ID（更新时使用）
     * @return 存在的数量
     */
    int checkNameUnique(@Param("tenantId") Long tenantId, 
                       @Param("dataMonth") Date dataMonth, 
                       @Param("name") String name, 
                       @Param("id") Long id);

    /**
     * 检查部门编码是否唯一（在同一租户和月份下）
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param deptCode 部门编码
     * @param id 排除的部门ID（更新时使用）
     * @return 存在的数量
     */
    int checkCodeUnique(@Param("tenantId") Long tenantId, 
                       @Param("dataMonth") Date dataMonth, 
                       @Param("deptCode") String deptCode, 
                       @Param("id") Long id);
}