<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.opsli.modulars.system.employee.mapper.EmployeeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="employeeResultMap" type="org.opsli.modulars.system.employee.entity.EmployeeMonthly">
        <id column="tenant_id" property="tenantId" />
        <id column="data_month" property="dataMonth" />
        <id column="id" property="id" />
        <result column="employee_number" property="employeeNumber" />
        <result column="full_name" property="fullName" />
        <result column="gender" property="gender" />
        <result column="date_of_birth" property="dateOfBirth" />
        <result column="phone_number" property="phoneNumber" />
        <result column="id_card_number" property="idCardNumber" />
        <result column="department_id" property="departmentId" />
        <result column="position_id" property="positionId" />
        <result column="concurrent_position_id" property="concurrentPositionId" />
        <result column="hire_date" property="hireDate" />
        <result column="status" property="status" />
        <result column="has_social_insurance" property="hasSocialInsurance" />
        <result column="is_contract_signed" property="isContractSigned" />
        <result column="is_probation_passed" property="isProbationPassed" />
        <result column="are_documents_signed" property="areDocumentsSigned" />
        <result column="personal_bio" property="personalBio" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        tenant_id, data_month, id, employee_number, full_name, gender, date_of_birth, 
        phone_number, id_card_number, department_id, position_id, concurrent_position_id, 
        hire_date, status, has_social_insurance, is_contract_signed, is_probation_passed, 
        are_documents_signed, personal_bio, version, deleted, create_by, create_time, 
        update_by, update_time
    </sql>

    <!-- 根据部门ID查询员工列表 -->
    <select id="findByDepartmentId" resultMap="employeeResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employees_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        AND department_id = #{departmentId}
        AND deleted = 0
    </select>

    <!-- 根据职位ID查询员工列表 -->
    <select id="findByPositionId" resultMap="employeeResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM employees_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        AND (position_id = #{positionId} OR concurrent_position_id = #{positionId})
        AND deleted = 0
    </select>

    <!-- 检查员工编号是否唯一（在同一租户和月份下） -->
    <select id="checkEmployeeNumberUnique" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM employees_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        AND employee_number = #{employeeNumber}
        AND deleted = 0
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <!-- 检查身份证号是否唯一（在同一租户和月份下） -->
    <select id="checkIdCardNumberUnique" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM employees_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        AND id_card_number = #{idCardNumber}
        AND deleted = 0
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

</mapper>
