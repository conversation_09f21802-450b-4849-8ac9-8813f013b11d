/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.employee.service;

import org.opsli.api.wrapper.system.employee.EmployeeModel;
import org.opsli.core.base.service.interfaces.CrudServiceInterface;
import org.opsli.modulars.system.employee.entity.EmployeeMonthly;

import java.util.Date;
import java.util.List;

/**
 * 员工管理 - 按月份 Service
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IEmployeeService extends CrudServiceInterface<EmployeeMonthly, EmployeeModel> {

    /**
     * 根据员工编号检查唯一性（在同一租户和月份下）
     *
     * @param model 员工模型
     * @return 是否唯一
     */
    boolean checkEmployeeNumberUnique(EmployeeModel model);

    /**
     * 根据身份证号检查唯一性（在同一租户和月份下）
     *
     * @param model 员工模型
     * @return 是否唯一
     */
    boolean checkIdCardNumberUnique(EmployeeModel model);

    /**
     * 根据部门ID获取员工列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param departmentId 部门ID
     * @return 员工列表
     */
    List<EmployeeModel> findByDepartmentId(Long tenantId, Date dataMonth, Long departmentId);

    /**
     * 根据职位ID获取员工列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 职位ID
     * @return 员工列表
     */
    List<EmployeeModel> findByPositionId(Long tenantId, Date dataMonth, Long positionId);
}
