<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.opsli.modulars.system.attendance.mapper.AttendanceMapper">

    <!-- 根据员工ID查询考勤列表 -->
    <select id="findByEmployeeId" resultType="org.opsli.modulars.system.attendance.entity.AttendanceMonthly">
        SELECT * FROM attendances_monthly
        WHERE tenant_id = #{tenantId}
          AND data_month = #{dataMonth}
          AND employee_id = #{employeeId}
          AND deleted = 0
        ORDER BY attendance_date DESC
    </select>

    <!-- 根据部门ID查询考勤列表 -->
    <select id="findByDepartmentId" resultType="org.opsli.modulars.system.attendance.entity.AttendanceMonthly">
        SELECT * FROM attendances_monthly
        WHERE tenant_id = #{tenantId}
          AND data_month = #{dataMonth}
          AND department_id = #{departmentId}
          AND deleted = 0
        ORDER BY attendance_date DESC, employee_id
    </select>

    <!-- 根据考勤状态查询考勤列表 -->
    <select id="findByStatus" resultType="org.opsli.modulars.system.attendance.entity.AttendanceMonthly">
        SELECT * FROM attendances_monthly
        WHERE tenant_id = #{tenantId}
          AND data_month = #{dataMonth}
          AND status = #{status}
          AND deleted = 0
        ORDER BY attendance_date DESC, employee_id
    </select>

    <!-- 检查考勤记录是否存在 -->
    <select id="checkAttendanceExists" resultType="int">
        SELECT COUNT(1) FROM attendances_monthly
        WHERE tenant_id = #{tenantId}
          AND data_month = #{dataMonth}
          AND employee_id = #{employeeId}
          AND attendance_date = #{attendanceDate}
          AND deleted = 0
        <if test="id != null">
          AND id != #{id}
        </if>
    </select>

    <!-- 根据日期范围查询考勤列表 -->
    <select id="findByDateRange" resultType="org.opsli.modulars.system.attendance.entity.AttendanceMonthly">
        SELECT * FROM attendances_monthly
        WHERE tenant_id = #{tenantId}
          AND data_month = #{dataMonth}
          AND attendance_date BETWEEN #{startDate} AND #{endDate}
          AND deleted = 0
        ORDER BY attendance_date DESC, employee_id
    </select>

    <!-- 统计考勤状态数量 -->
    <select id="countByStatus" resultType="int">
        SELECT COUNT(1) FROM attendances_monthly
        WHERE tenant_id = #{tenantId}
          AND data_month = #{dataMonth}
          AND status = #{status}
          AND deleted = 0
    </select>

</mapper>
