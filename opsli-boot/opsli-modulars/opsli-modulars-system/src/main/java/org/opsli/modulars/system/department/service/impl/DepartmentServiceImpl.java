/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.department.service.impl;

import cn.hutool.core.collection.CollUtil;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opsli.api.wrapper.system.department.DepartmentModel;

import org.opsli.common.exception.ServiceException;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.core.base.service.impl.CrudServiceImpl;
import org.opsli.modulars.system.department.entity.DepartmentMonthly;
import org.opsli.modulars.system.department.mapper.DepartmentMapper;
import org.opsli.modulars.system.SystemMsg;
import org.opsli.modulars.system.department.service.IDepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 部门管理 - 按月份 Service 实现类
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Service
public class DepartmentServiceImpl extends CrudServiceImpl<DepartmentMapper, DepartmentMonthly, DepartmentModel>
        implements IDepartmentService {


    @Autowired(required = false)
    private DepartmentMapper mapper;

    @Override
    public boolean checkNameUnique(DepartmentModel model) {
        if (model == null) {
            return false;
        }
        
        Long id = null;
        if (StringUtils.isNotBlank(model.getId())) {
            id = Long.valueOf(model.getId());
        }
        
        int count = mapper.checkNameUnique(
                model.getTenantId(), 
                model.getDataMonth(), 
                model.getName(), 
                id);
        
        return count == 0;
    }

    @Override
    public boolean checkCodeUnique(DepartmentModel model) {
        if (model == null) {
            return false;
        }
        
        Long id = null;
        if (StringUtils.isNotBlank(model.getId())) {
            id = Long.valueOf(model.getId());
        }
        
        int count = mapper.checkCodeUnique(
                model.getTenantId(), 
                model.getDataMonth(), 
                model.getDeptCode(), 
                id);
        
        return count == 0;
    }

    @Override
    public List<DepartmentModel> findByParentId(Long tenantId, Date dataMonth, Long parentId) {
        List<DepartmentMonthly> entityList = mapper.findByParentId(tenantId, dataMonth, parentId);
        return WrapperUtil.transformInstance(entityList, modelClazz);
    }

    @Override
    public boolean hasChildren(Long tenantId, Date dataMonth, Long departmentId) {
        List<DepartmentMonthly> children = mapper.findByParentId(tenantId, dataMonth, departmentId);
        return CollUtil.isNotEmpty(children);
    }

    @Override
    public boolean validateParentDepartment(DepartmentModel model) {
        if (model == null || model.getParentDepartmentId() == null) {
            return true;
        }
        
        // 不能选择自己作为父部门
        if (StringUtils.isNotBlank(model.getId()) && 
                model.getParentDepartmentId().equals(Long.valueOf(model.getId()))) {
            return false;
        }
        
        // 不能选择自己的子部门作为父部门
        if (StringUtils.isNotBlank(model.getId())) {
            List<Long> childrenIds = getChildrenIds(
                    model.getTenantId(), 
                    model.getDataMonth(), 
                    Long.valueOf(model.getId()));
            
            return !childrenIds.contains(model.getParentDepartmentId());
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DepartmentModel insert(DepartmentModel model) {
        // 验证部门名称唯一性
        if (!checkNameUnique(model)) {
            throw new ServiceException(SystemMsg.EXCEPTION_DEPT_NAME_UNIQUE);
        }
        
        // 验证部门编码唯一性
        if (!checkCodeUnique(model)) {
            throw new ServiceException(SystemMsg.EXCEPTION_DEPT_CODE_UNIQUE);
        }
        
        // 验证父部门合法性
        if (!validateParentDepartment(model)) {
            throw new ServiceException(SystemMsg.EXCEPTION_DEPT_PARENT_ILLEGAL);
        }
        
        return super.insert(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DepartmentModel update(DepartmentModel model) {
        // 验证部门名称唯一性
        if (!checkNameUnique(model)) {
            throw new ServiceException(SystemMsg.EXCEPTION_DEPT_NAME_UNIQUE);
        }
        
        // 验证部门编码唯一性
        if (!checkCodeUnique(model)) {
            throw new ServiceException(SystemMsg.EXCEPTION_DEPT_CODE_UNIQUE);
        }
        
        // 验证父部门合法性
        if (!validateParentDepartment(model)) {
            throw new ServiceException(SystemMsg.EXCEPTION_DEPT_PARENT_ILLEGAL);
        }
        
        return super.update(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String id) {
        if (StringUtils.isBlank(id)) {
            return false;
        }
        
        DepartmentModel model = super.get(id);
        if (model == null) {
            return false;
        }
        
        // 检查是否有子部门
        if (hasChildren(model.getTenantId(), model.getDataMonth(), Long.valueOf(id))) {
            throw new ServiceException(SystemMsg.EXCEPTION_DEPT_HAS_CHILDREN);
        }
        
        return super.delete(id);
    }

    /**
     * 获取所有子部门ID列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param departmentId 部门ID
     * @return 子部门ID列表
     */
    private List<Long> getChildrenIds(Long tenantId, Date dataMonth, Long departmentId) {
        List<Long> childrenIds = new ArrayList<>();
        List<DepartmentMonthly> children = mapper.findByParentId(tenantId, dataMonth, departmentId);
        
        if (CollUtil.isNotEmpty(children)) {
            for (DepartmentMonthly child : children) {
                childrenIds.add(Long.valueOf(child.getId()));
                // 递归获取子部门的子部门
                childrenIds.addAll(getChildrenIds(tenantId, dataMonth, Long.valueOf(child.getId())));
            }
        }
        
        return childrenIds;
    }
}