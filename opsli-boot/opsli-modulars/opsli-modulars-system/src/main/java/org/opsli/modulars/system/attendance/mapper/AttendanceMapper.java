/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.attendance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.opsli.modulars.system.attendance.entity.AttendanceMonthly;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 考勤管理 - 按月份 Mapper
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Mapper
public interface AttendanceMapper extends BaseMapper<AttendanceMonthly> {

    /**
     * 根据员工ID查询考勤列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @return 考勤列表
     */
    List<AttendanceMonthly> findByEmployeeId(@Param("tenantId") Long tenantId, 
                                           @Param("dataMonth") Date dataMonth, 
                                           @Param("employeeId") Long employeeId);

    /**
     * 根据部门ID查询考勤列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param departmentId 部门ID
     * @return 考勤列表
     */
    List<AttendanceMonthly> findByDepartmentId(@Param("tenantId") Long tenantId, 
                                             @Param("dataMonth") Date dataMonth, 
                                             @Param("departmentId") Long departmentId);

    /**
     * 根据考勤状态查询考勤列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param status 考勤状态
     * @return 考勤列表
     */
    List<AttendanceMonthly> findByStatus(@Param("tenantId") Long tenantId, 
                                       @Param("dataMonth") Date dataMonth, 
                                       @Param("status") String status);

    /**
     * 检查考勤记录是否存在（同一租户、月份、员工、日期下）
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @param attendanceDate 考勤日期
     * @param id 排除的考勤记录ID（更新时使用）
     * @return 记录数量
     */
    int checkAttendanceExists(@Param("tenantId") Long tenantId, 
                            @Param("dataMonth") Date dataMonth, 
                            @Param("employeeId") Long employeeId, 
                            @Param("attendanceDate") LocalDate attendanceDate, 
                            @Param("id") Long id);

    /**
     * 根据日期范围查询考勤列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 考勤列表
     */
    List<AttendanceMonthly> findByDateRange(@Param("tenantId") Long tenantId, 
                                          @Param("dataMonth") Date dataMonth, 
                                          @Param("startDate") LocalDate startDate, 
                                          @Param("endDate") LocalDate endDate);

    /**
     * 统计考勤状态数量
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param status 考勤状态
     * @return 数量
     */
    int countByStatus(@Param("tenantId") Long tenantId, 
                     @Param("dataMonth") Date dataMonth, 
                     @Param("status") String status);
}
