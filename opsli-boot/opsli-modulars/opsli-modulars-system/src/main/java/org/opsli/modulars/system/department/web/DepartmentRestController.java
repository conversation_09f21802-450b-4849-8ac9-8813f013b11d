/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.department.web;

import cn.hutool.core.convert.Convert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.web.system.department.DepartmentApi;
import org.opsli.api.wrapper.system.department.DepartmentModel;
import org.opsli.common.annotation.ApiRestController;
import org.opsli.core.base.controller.BaseRestController;
import org.opsli.core.log.annotation.OperateLogger;
import org.opsli.core.log.enums.ModuleEnum;
import org.opsli.core.log.enums.OperationTypeEnum;
import org.opsli.common.utils.WrapperUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.opsli.core.persistence.Page;
import org.opsli.core.persistence.querybuilder.WebQueryBuilder;
import org.opsli.modulars.system.department.entity.DepartmentMonthly;
import org.opsli.modulars.system.department.service.IDepartmentService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.security.access.prepost.PreAuthorize;

import jakarta.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 部门管理 - 按月份 Controller
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Tag(name = DepartmentApi.TITLE, description = DepartmentApi.SUB_TITLE)
@Slf4j
@ApiRestController("/{ver}/system/department")
public class DepartmentRestController extends BaseRestController<DepartmentMonthly, DepartmentModel, IDepartmentService>
        implements DepartmentApi {

    /**
     * 部门管理 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "获得单条部门数据")
    @PreAuthorize("hasAuthority('system_department_select')")
    @Override
    public ResultWrapper<DepartmentModel> get(DepartmentModel model) {
        model = IService.get(model);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 部门管理 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得分页数据")
    @PreAuthorize("hasAuthority('system_dept_select')")
    @Override
    public ResultWrapper<?> findPage(Integer pageNo, Integer pageSize, HttpServletRequest request) {
        // 处理请求参数
        Map<String, String[]> parameterMap = new HashMap<>(request.getParameterMap());
        String dataMonthStr = null;
        if (parameterMap.containsKey("dataMonth")) {
            dataMonthStr = parameterMap.get("dataMonth")[0];
            parameterMap.remove("dataMonth");
        }

        WebQueryBuilder<DepartmentMonthly> queryBuilder = new WebQueryBuilder<>(IService.getEntityClass(), parameterMap);
        QueryWrapper<DepartmentMonthly> queryWrapper = queryBuilder.build();

        // 手动处理 dataMonth
        if (StringUtils.isNotEmpty(dataMonthStr)) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
                Date startDate = sdf.parse(dataMonthStr);

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);
                calendar.add(Calendar.MONTH, 1);
                calendar.add(Calendar.DAY_OF_MONTH, -1);
                Date endDate = calendar.getTime();

                queryWrapper.between("data_month", startDate, endDate);
            } catch (ParseException e) {
                log.error("日期转换失败: {}, 异常: {}", dataMonthStr, e.getMessage());
            }
        }

        Page<DepartmentMonthly, DepartmentModel> page = new Page<>(pageNo, pageSize);
        page.setQueryWrapper(queryWrapper);
        page = IService.findPage(page);

        return ResultWrapper.getSuccessResultWrapper(page.getPageData());
    }

    /**
     * 部门管理 查询所有
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得所有数据")
    @GetMapping("listAll")
    public ResultWrapper<?> listAll(HttpServletRequest request) {
        // 处理请求参数
        Map<String, String[]> parameterMap = new HashMap<>(request.getParameterMap());
        String dataMonthStr = null;
        if (parameterMap.containsKey("dataMonth")) {
            dataMonthStr = parameterMap.get("dataMonth")[0];
            parameterMap.remove("dataMonth");
        }

        WebQueryBuilder<DepartmentMonthly> queryBuilder = new WebQueryBuilder<>(IService.getEntityClass(), parameterMap);
        QueryWrapper<DepartmentMonthly> queryWrapper = queryBuilder.build();

        // 手动处理 dataMonth
        if (StringUtils.isNotEmpty(dataMonthStr)) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
                Date startDate = sdf.parse(dataMonthStr);

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);
                calendar.add(Calendar.MONTH, 1);
                calendar.add(Calendar.DAY_OF_MONTH, -1);
                Date endDate = calendar.getTime();

                queryWrapper.between("data_month", startDate, endDate);
            } catch (ParseException e) {
                log.error("日期转换失败: {}, 异常: {}", dataMonthStr, e.getMessage());
            }
        }

        List<DepartmentMonthly> entityList = IService.findList(queryWrapper);
        List<DepartmentModel> modelList = WrapperUtil.transformInstance(entityList, IService.getModelClass());
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 部门管理 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "新增部门数据")
    @PreAuthorize("hasAuthority('system_dept_insert')")
    @OperateLogger(description = "新增部门数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> insert(DepartmentModel model) {
        // 调用新增方法
        IService.insert(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("新增部门数据成功");
    }

    /**
     * 部门管理 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "修改部门数据")
    @PreAuthorize("hasAuthority('system_dept_update')")
    @OperateLogger(description = "修改部门数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> update(DepartmentModel model) {
        // 调用修改方法
        IService.update(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("修改部门数据成功");
    }

    /**
     * 部门管理 删除
     * @param id ID
     * @return ResultWrapper
     */
    @Operation(summary = "删除部门数据")
    @PreAuthorize("hasAuthority('system_dept_delete')")
    @OperateLogger(description = "删除部门数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> del(String id) {
        IService.delete(id);
        return ResultWrapper.getSuccessResultWrapperByMsg("删除部门数据成功");
    }

    /**
     * 部门管理 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @Operation(summary = "批量删除部门数据")
    @PreAuthorize("hasAuthority('system_dept_delete')")
    @OperateLogger(description = "批量删除部门数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> delAll(String ids) {
        String[] idArray = Convert.toStrArray(ids);
        IService.deleteAll(idArray);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量删除部门数据成功");
    }

    /**
     * 根据父部门ID获取子部门列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param parentId 父部门ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据父部门ID获取子部门列表")
    @PreAuthorize("hasAuthority('system_dept_select')")
    @Override
    public ResultWrapper<List<DepartmentModel>> findByParentId(Long tenantId, String dataMonth, Long parentId) {
        // Convert string to Date if needed
        Date date = null;
        if (dataMonth != null) {
            try {
                // 将 YYYY-MM 格式的字符串转换为该月的第一天
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM");
                date = sdf.parse(dataMonth);
            } catch (java.text.ParseException e) {
                // 处理日期解析错误
                log.error("Error parsing date: {}", dataMonth, e);
            }
        }
        List<DepartmentModel> departmentList = IService.findByParentId(tenantId, date, parentId);
        return ResultWrapper.getSuccessResultWrapper(departmentList);
    }

    /**
     * 检查部门名称是否唯一
     *
     * @param model 部门模型
     * @return ResultWrapper
     */
    @Operation(summary = "检查部门名称是否唯一")
    @Override
    public ResultWrapper<Boolean> checkNameUnique(DepartmentModel model) {
        boolean isUnique = IService.checkNameUnique(model);
        return ResultWrapper.getSuccessResultWrapper(isUnique);
    }

    /**
     * 检查部门编码是否唯一
     *
     * @param model 部门模型
     * @return ResultWrapper
     */
    @Operation(summary = "检查部门编码是否唯一")
    @Override
    public ResultWrapper<Boolean> checkCodeUnique(DepartmentModel model) {
        boolean isUnique = IService.checkCodeUnique(model);
        return ResultWrapper.getSuccessResultWrapper(isUnique);
    }
}