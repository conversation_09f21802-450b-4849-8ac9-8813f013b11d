/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.attendance.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.opsli.core.base.entity.BaseEntity;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;

/**
 * 考勤管理 - 按月份
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("attendances_monthly")
public class AttendanceMonthly extends BaseEntity {

    /** 租户ID */
    private Long tenantId;
    
    /** 数据月份 */
    private Date dataMonth;
    
    /** 考勤记录ID */
    @TableId
    private String id;
    
    /** 员工ID */
    private Long employeeId;
    
    /** 部门ID */
    private Long departmentId;
    
    /** 考勤日期 */
    private LocalDate attendanceDate;
    
    /** 签到时间 */
    private LocalTime checkInTime;
    
    /** 签退时间 */
    private LocalTime checkOutTime;
    
    /** 考勤状态：正常, 迟到, 早退, 旷工, 请假 */
    private String status;
    
    /** 是否纳入统计：1-是，0-否 */
    private Integer includeInStats;
    
    /** 备注 */
    private String remarks;
    
    /** 版本号 */
    @Version
    private Integer version;
    
    /** 逻辑删除标识：0-未删除，1-已删除 */
    @TableLogic
    private Integer deleted;
    
    /** 创建人 */
    private String createBy;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新人 */
    private String updateBy;
    
    /** 更新时间 */
    private Date updateTime;
}
