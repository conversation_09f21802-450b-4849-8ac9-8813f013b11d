/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.department.service;

import org.opsli.api.wrapper.system.department.DepartmentModel;
import org.opsli.core.base.service.interfaces.CrudServiceInterface;
import org.opsli.modulars.system.department.entity.DepartmentMonthly;

import java.util.Date;
import java.util.List;

/**
 * 部门管理 - 按月份 Service
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IDepartmentService extends CrudServiceInterface<DepartmentMonthly, DepartmentModel> {

    /**
     * 根据部门名称检查唯一性（在同一租户和月份下）
     *
     * @param model 部门模型
     * @return 是否唯一
     */
    boolean checkNameUnique(DepartmentModel model);

    /**
     * 根据部门编码检查唯一性（在同一租户和月份下）
     *
     * @param model 部门模型
     * @return 是否唯一
     */
    boolean checkCodeUnique(DepartmentModel model);

    /**
     * 根据父部门ID获取子部门列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<DepartmentModel> findByParentId(Long tenantId, Date dataMonth, Long parentId);

    /**
     * 检查部门是否有子部门
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param departmentId 部门ID
     * @return 是否有子部门
     */
    boolean hasChildren(Long tenantId, Date dataMonth, Long departmentId);

    /**
     * 验证父部门是否合法
     * 1. 不能选择自己作为父部门
     * 2. 不能选择自己的子部门作为父部门
     *
     * @param model 部门模型
     * @return 是否合法
     */
    boolean validateParentDepartment(DepartmentModel model);
}