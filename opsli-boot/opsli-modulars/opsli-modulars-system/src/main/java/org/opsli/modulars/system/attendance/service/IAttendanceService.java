/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.attendance.service;

import org.opsli.api.wrapper.system.attendance.AttendanceModel;
import org.opsli.core.base.service.interfaces.CrudServiceInterface;
import org.opsli.modulars.system.attendance.entity.AttendanceMonthly;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 考勤管理 - 按月份 Service
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IAttendanceService extends CrudServiceInterface<AttendanceMonthly, AttendanceModel> {

    /**
     * 检查考勤记录是否存在（同一租户、月份、员工、日期下）
     *
     * @param model 考勤模型
     * @return 是否存在
     */
    boolean checkAttendanceExists(AttendanceModel model);

    /**
     * 根据员工ID获取考勤列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @return 考勤列表
     */
    List<AttendanceModel> findByEmployeeId(Long tenantId, Date dataMonth, Long employeeId);

    /**
     * 根据部门ID获取考勤列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param departmentId 部门ID
     * @return 考勤列表
     */
    List<AttendanceModel> findByDepartmentId(Long tenantId, Date dataMonth, Long departmentId);

    /**
     * 根据考勤状态获取考勤列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param status 考勤状态
     * @return 考勤列表
     */
    List<AttendanceModel> findByStatus(Long tenantId, Date dataMonth, String status);

    /**
     * 根据日期范围获取考勤列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 考勤列表
     */
    List<AttendanceModel> findByDateRange(Long tenantId, Date dataMonth, LocalDate startDate, LocalDate endDate);

    /**
     * 统计考勤状态数量
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param status 考勤状态
     * @return 数量
     */
    int countByStatus(Long tenantId, Date dataMonth, String status);
}
