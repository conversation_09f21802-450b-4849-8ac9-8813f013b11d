/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.attendance.web;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.web.system.attendance.AttendanceApi;
import org.opsli.api.wrapper.system.attendance.AttendanceModel;
import org.opsli.common.annotation.ApiRestController;
import org.opsli.core.log.annotation.OperateLogger;
import org.opsli.core.log.enums.ModuleEnum;
import org.opsli.core.log.enums.OperationTypeEnum;
import org.opsli.core.base.controller.BaseRestController;
import org.opsli.core.persistence.Page;
import org.opsli.core.persistence.querybuilder.WebQueryBuilder;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.modulars.system.attendance.entity.AttendanceMonthly;
import org.opsli.modulars.system.attendance.service.IAttendanceService;
import org.springframework.security.access.prepost.PreAuthorize;

import jakarta.servlet.http.HttpServletRequest;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 考勤管理 - 按月份 Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Tag(name = AttendanceApi.TITLE, description = AttendanceApi.SUB_TITLE)
@Slf4j
@ApiRestController("/{ver}/system/attendance")
public class AttendanceRestController extends BaseRestController<AttendanceMonthly, AttendanceModel, IAttendanceService>
        implements AttendanceApi {

    /**
     * 考勤管理 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "获得单条考勤数据")
    @PreAuthorize("hasAuthority('system_attendance_select')")
    @Override
    public ResultWrapper<AttendanceModel> get(AttendanceModel model) {
        model = IService.get(model);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 考勤管理 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得分页考勤数据")
    @PreAuthorize("hasAuthority('system_attendance_select')")
    @Override
    public ResultWrapper<?> findPage(Integer pageNo, Integer pageSize, HttpServletRequest request) {
        // 处理请求参数
        Map<String, String[]> parameterMap = new HashMap<>(request.getParameterMap());
        String dataMonthStr = null;
        if (parameterMap.containsKey("dataMonth")) {
            dataMonthStr = parameterMap.get("dataMonth")[0];
            parameterMap.remove("dataMonth");
        }

        WebQueryBuilder<AttendanceMonthly> queryBuilder = new WebQueryBuilder<>(IService.getEntityClass(), parameterMap);
        QueryWrapper<AttendanceMonthly> queryWrapper = queryBuilder.build();

        // 手动处理 dataMonth
        if (StringUtils.isNotEmpty(dataMonthStr)) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM");
                Date startDate = sdf.parse(dataMonthStr);

                java.util.Calendar calendar = java.util.Calendar.getInstance();
                calendar.setTime(startDate);
                calendar.add(java.util.Calendar.MONTH, 1);
                calendar.add(java.util.Calendar.DAY_OF_MONTH, -1);
                Date endDate = calendar.getTime();

                queryWrapper.between("data_month", startDate, endDate);
            } catch (java.text.ParseException e) {
                log.error("日期转换失败: {}, 异常: {}", dataMonthStr, e.getMessage());
            }
        }

        Page<AttendanceMonthly, AttendanceModel> page = new Page<>(pageNo, pageSize);
        page.setQueryWrapper(queryWrapper);
        page = IService.findPage(page);

        return ResultWrapper.getSuccessResultWrapper(page.getPageData());
    }

    /**
     * 考勤管理 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得全部考勤数据")
    @PreAuthorize("hasAuthority('system_attendance_select')")
    @Override
    public ResultWrapper<?> listAll(HttpServletRequest request) {
        Map<String, String[]> parameterMap = new HashMap<>(request.getParameterMap());
        WebQueryBuilder<AttendanceMonthly> queryBuilder = new WebQueryBuilder<>(IService.getEntityClass(), parameterMap);
        QueryWrapper<AttendanceMonthly> queryWrapper = queryBuilder.build();

        List<AttendanceMonthly> entityList = IService.findList(queryWrapper);
        List<AttendanceModel> modelList = WrapperUtil.transformInstance(entityList, IService.getModelClass());
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 考勤管理 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "新增考勤数据")
    @PreAuthorize("hasAuthority('system_attendance_insert')")
    @OperateLogger(description = "新增考勤数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> insert(AttendanceModel model) {
        // 调用新增方法
        IService.insert(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("新增考勤数据成功");
    }

    /**
     * 考勤管理 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "修改考勤数据")
    @PreAuthorize("hasAuthority('system_attendance_update')")
    @OperateLogger(description = "修改考勤数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> update(AttendanceModel model) {
        // 调用修改方法
        IService.update(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("修改考勤数据成功");
    }

    /**
     * 考勤管理 删除
     * @param id ID
     * @return ResultWrapper
     */
    @Operation(summary = "删除考勤数据")
    @PreAuthorize("hasAuthority('system_attendance_delete')")
    @OperateLogger(description = "删除考勤数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> del(String id) {
        IService.delete(id);
        return ResultWrapper.getSuccessResultWrapperByMsg("删除考勤数据成功");
    }

    /**
     * 考勤管理 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @Operation(summary = "批量删除考勤数据")
    @PreAuthorize("hasAuthority('system_attendance_delete')")
    @OperateLogger(description = "批量删除考勤数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> delAll(String ids) {
        String[] idArray = Convert.toStrArray(ids);
        IService.deleteAll(idArray);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量删除考勤数据成功");
    }

    /**
     * 根据员工ID获取考勤列表
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "根据员工ID获取考勤列表")
    @PreAuthorize("hasAuthority('system_attendance_select')")
    @Override
    public ResultWrapper<?> findByEmployeeId(HttpServletRequest request) {
        String tenantIdStr = request.getParameter("tenantId");
        String dataMonthStr = request.getParameter("dataMonth");
        String employeeIdStr = request.getParameter("employeeId");

        if (StrUtil.isBlank(tenantIdStr) || StrUtil.isBlank(dataMonthStr) || StrUtil.isBlank(employeeIdStr)) {
            return ResultWrapper.getErrorResultWrapper("参数不能为空");
        }

        Long tenantId = Convert.toLong(tenantIdStr);
        Date dataMonth = Convert.toDate(dataMonthStr);
        Long employeeId = Convert.toLong(employeeIdStr);

        List<AttendanceModel> modelList = IService.findByEmployeeId(tenantId, dataMonth, employeeId);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据部门ID获取考勤列表
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "根据部门ID获取考勤列表")
    @PreAuthorize("hasAuthority('system_attendance_select')")
    @Override
    public ResultWrapper<?> findByDepartmentId(HttpServletRequest request) {
        String tenantIdStr = request.getParameter("tenantId");
        String dataMonthStr = request.getParameter("dataMonth");
        String departmentIdStr = request.getParameter("departmentId");

        if (StrUtil.isBlank(tenantIdStr) || StrUtil.isBlank(dataMonthStr) || StrUtil.isBlank(departmentIdStr)) {
            return ResultWrapper.getErrorResultWrapper("参数不能为空");
        }

        Long tenantId = Convert.toLong(tenantIdStr);
        Date dataMonth = Convert.toDate(dataMonthStr);
        Long departmentId = Convert.toLong(departmentIdStr);

        List<AttendanceModel> modelList = IService.findByDepartmentId(tenantId, dataMonth, departmentId);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据考勤状态获取考勤列表
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "根据考勤状态获取考勤列表")
    @PreAuthorize("hasAuthority('system_attendance_select')")
    @Override
    public ResultWrapper<?> findByStatus(HttpServletRequest request) {
        String tenantIdStr = request.getParameter("tenantId");
        String dataMonthStr = request.getParameter("dataMonth");
        String status = request.getParameter("status");

        if (StrUtil.isBlank(tenantIdStr) || StrUtil.isBlank(dataMonthStr) || StrUtil.isBlank(status)) {
            return ResultWrapper.getErrorResultWrapper("参数不能为空");
        }

        Long tenantId = Convert.toLong(tenantIdStr);
        Date dataMonth = Convert.toDate(dataMonthStr);

        List<AttendanceModel> modelList = IService.findByStatus(tenantId, dataMonth, status);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }
}
