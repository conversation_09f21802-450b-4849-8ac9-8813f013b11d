/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.employee.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.opsli.core.base.entity.BaseEntity;

import java.util.Date;

/**
 * 员工管理 - 按月份
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("employees_monthly")
public class EmployeeMonthly extends BaseEntity {

    /** 租户ID */
    private Long tenantId;
    
    /** 数据月份 */
    private Date dataMonth;
    
    /** 员工ID */
    @TableId
    private String id;
    
    /** 员工编号 */
    private String employeeNumber;
    
    /** 姓名 */
    private String fullName;
    
    /** 性别 */
    private String gender;
    
    /** 出生日期 */
    private Date dateOfBirth;
    
    /** 手机号码 */
    private String phoneNumber;
    
    /** 身份证号 */
    private String idCardNumber;
    
    /** 关联部门ID */
    private Long departmentId;
    
    /** 主职位ID */
    private Long positionId;
    
    /** 兼岗位ID */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long concurrentPositionId;
    
    /** 入职日期 */
    private Date hireDate;
    
    /** 员工状态 */
    private String status;
    
    /** 是否缴纳社保 */
    private Boolean hasSocialInsurance;
    
    /** 是否签订合同 */
    private Boolean isContractSigned;
    
    /** 是否已转正 */
    private Boolean isProbationPassed;
    
    /** 是否签订资料 */
    private Boolean areDocumentsSigned;
    
    /** 个人简介 */
    private String personalBio;
    
    /** 版本号 */
    @Version
    private Integer version;
    
    /** 逻辑删除标识：0-未删除，1-已删除 */
    @TableLogic
    private Integer deleted;
    
    /** 创建人 */
    private String createBy;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新人 */
    private String updateBy;
    
    /** 更新时间 */
    private Date updateTime;
}
