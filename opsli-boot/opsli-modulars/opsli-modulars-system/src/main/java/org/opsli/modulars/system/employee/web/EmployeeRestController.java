/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.employee.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.web.system.employee.EmployeeApi;
import org.opsli.api.wrapper.system.employee.EmployeeModel;
import org.opsli.common.annotation.ApiRestController;
import org.opsli.core.log.annotation.OperateLogger;
import org.opsli.core.log.enums.ModuleEnum;
import org.opsli.core.log.enums.OperationTypeEnum;
import org.opsli.core.base.controller.BaseRestController;
import org.opsli.core.persistence.Page;
import org.opsli.core.persistence.querybuilder.WebQueryBuilder;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.modulars.system.employee.entity.EmployeeMonthly;
import org.opsli.modulars.system.employee.service.IEmployeeService;
import org.springframework.security.access.prepost.PreAuthorize;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 员工管理 - 按月份 Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Tag(name = EmployeeApi.TITLE, description = EmployeeApi.SUB_TITLE)
@Slf4j
@ApiRestController("/{ver}/system/employee")
public class EmployeeRestController extends BaseRestController<EmployeeMonthly, EmployeeModel, IEmployeeService>
        implements EmployeeApi {

    /**
     * 员工管理 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "获得单条员工数据")
    @PreAuthorize("hasAuthority('system_employee_select')")
    @Override
    public ResultWrapper<EmployeeModel> get(EmployeeModel model) {
        model = IService.get(model);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 员工管理 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得分页数据")
    @PreAuthorize("hasAuthority('system_employee_select')")
    @Override
    public ResultWrapper<?> findPage(Integer pageNo, Integer pageSize, HttpServletRequest request) {
        // 处理请求参数
        Map<String, String[]> parameterMap = new HashMap<>(request.getParameterMap());
        String dataMonthStr = null;
        if (parameterMap.containsKey("dataMonth")) {
            dataMonthStr = parameterMap.get("dataMonth")[0];
            parameterMap.remove("dataMonth");
        }

        WebQueryBuilder<EmployeeMonthly> queryBuilder = new WebQueryBuilder<>(IService.getEntityClass(), parameterMap);
        QueryWrapper<EmployeeMonthly> queryWrapper = queryBuilder.build();

        // 手动处理 dataMonth
        if (StringUtils.isNotEmpty(dataMonthStr)) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM");
                Date startDate = sdf.parse(dataMonthStr);

                java.util.Calendar calendar = java.util.Calendar.getInstance();
                calendar.setTime(startDate);
                calendar.add(java.util.Calendar.MONTH, 1);
                calendar.add(java.util.Calendar.DAY_OF_MONTH, -1);
                Date endDate = calendar.getTime();

                queryWrapper.between("data_month", startDate, endDate);
            } catch (java.text.ParseException e) {
                log.error("日期转换失败: {}, 异常: {}", dataMonthStr, e.getMessage());
            }
        }

        Page<EmployeeMonthly, EmployeeModel> page = new Page<>(pageNo, pageSize);
        page.setQueryWrapper(queryWrapper);
        page = IService.findPage(page);

        return ResultWrapper.getSuccessResultWrapper(page.getPageData());
    }

    /**
     * 员工管理 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得全部数据")
    @PreAuthorize("hasAuthority('system_employee_select')")
    @Override
    public ResultWrapper<?> listAll(HttpServletRequest request) {
        // 处理请求参数
        Map<String, String[]> parameterMap = new HashMap<>(request.getParameterMap());
        String dataMonthStr = null;
        if (parameterMap.containsKey("dataMonth")) {
            dataMonthStr = parameterMap.get("dataMonth")[0];
            parameterMap.remove("dataMonth");
        }

        WebQueryBuilder<EmployeeMonthly> queryBuilder = new WebQueryBuilder<>(IService.getEntityClass(), parameterMap);
        QueryWrapper<EmployeeMonthly> queryWrapper = queryBuilder.build();

        // 手动处理 dataMonth
        if (StringUtils.isNotEmpty(dataMonthStr)) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM");
                Date startDate = sdf.parse(dataMonthStr);

                java.util.Calendar calendar = java.util.Calendar.getInstance();
                calendar.setTime(startDate);
                calendar.add(java.util.Calendar.MONTH, 1);
                calendar.add(java.util.Calendar.DAY_OF_MONTH, -1);
                Date endDate = calendar.getTime();

                queryWrapper.between("data_month", startDate, endDate);
            } catch (java.text.ParseException e) {
                log.error("日期转换失败: {}, 异常: {}", dataMonthStr, e.getMessage());
            }
        }

        List<EmployeeMonthly> entityList = IService.findList(queryWrapper);
        List<EmployeeModel> modelList = WrapperUtil.transformInstance(entityList, IService.getModelClass());
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 员工管理 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "新增员工数据")
    @PreAuthorize("hasAuthority('system_employee_insert')")
    @OperateLogger(description = "新增员工数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> insert(EmployeeModel model) {
        // 调用新增方法
        IService.insert(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("新增员工数据成功");
    }

    /**
     * 员工管理 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "修改员工数据")
    @PreAuthorize("hasAuthority('system_employee_update')")
    @OperateLogger(description = "修改员工数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> update(EmployeeModel model) {
        // 调用修改方法
        IService.update(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("修改员工数据成功");
    }

    /**
     * 员工管理 删除
     * @param id ID
     * @return ResultWrapper
     */
    @Operation(summary = "删除员工数据")
    @PreAuthorize("hasAuthority('system_employee_delete')")
    @OperateLogger(description = "删除员工数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> del(String id) {
        IService.delete(id);
        return ResultWrapper.getSuccessResultWrapperByMsg("删除员工数据成功");
    }

    /**
     * 员工管理 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @Operation(summary = "批量删除员工数据")
    @PreAuthorize("hasAuthority('system_employee_delete')")
    @OperateLogger(description = "批量删除员工数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> delAll(String ids) {
        String[] idArray = ids.split(",");
        IService.deleteAll(idArray);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量删除员工数据成功");
    }

    /**
     * 根据部门ID获取员工列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param departmentId 部门ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据部门ID获取员工列表")
    @PreAuthorize("hasAuthority('system_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeModel>> findByDepartmentId(Long tenantId, String dataMonth, Long departmentId) {
        // Convert string to Date if needed
        Date date = null;
        if (dataMonth != null) {
            try {
                // 将 YYYY-MM 格式的字符串转换为该月的第一天
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM");
                date = sdf.parse(dataMonth);
            } catch (java.text.ParseException e) {
                // 处理日期解析错误
                log.error("Error parsing date: {}", dataMonth, e);
            }
        }
        List<EmployeeModel> employeeList = IService.findByDepartmentId(tenantId, date, departmentId);
        return ResultWrapper.getSuccessResultWrapper(employeeList);
    }

    /**
     * 根据职位ID获取员工列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 职位ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据职位ID获取员工列表")
    @PreAuthorize("hasAuthority('system_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeModel>> findByPositionId(Long tenantId, String dataMonth, Long positionId) {
        // Convert string to Date if needed
        Date date = null;
        if (dataMonth != null) {
            try {
                // 将 YYYY-MM 格式的字符串转换为该月的第一天
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM");
                date = sdf.parse(dataMonth);
            } catch (java.text.ParseException e) {
                // 处理日期解析错误
                log.error("Error parsing date: {}", dataMonth, e);
            }
        }
        List<EmployeeModel> employeeList = IService.findByPositionId(tenantId, date, positionId);
        return ResultWrapper.getSuccessResultWrapper(employeeList);
    }

    /**
     * 检查员工编号是否唯一
     *
     * @param model 员工模型
     * @return ResultWrapper
     */
    @Operation(summary = "检查员工编号是否唯一")
    @Override
    public ResultWrapper<Boolean> checkEmployeeNumberUnique(EmployeeModel model) {
        boolean isUnique = IService.checkEmployeeNumberUnique(model);
        return ResultWrapper.getSuccessResultWrapper(isUnique);
    }

    /**
     * 检查身份证号是否唯一
     *
     * @param model 员工模型
     * @return ResultWrapper
     */
    @Operation(summary = "检查身份证号是否唯一")
    @Override
    public ResultWrapper<Boolean> checkIdCardNumberUnique(EmployeeModel model) {
        boolean isUnique = IService.checkIdCardNumberUnique(model);
        return ResultWrapper.getSuccessResultWrapper(isUnique);
    }
}
