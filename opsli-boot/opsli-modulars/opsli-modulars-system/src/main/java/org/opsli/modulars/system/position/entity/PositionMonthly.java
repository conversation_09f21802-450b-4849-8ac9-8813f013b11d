/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.position.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.opsli.core.base.entity.BaseEntity;

import java.util.Date;

/**
 * 职位管理 - 按月份
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("positions_monthly")
public class PositionMonthly extends BaseEntity {

    /** 租户ID */
    private Long tenantId;
    
    /** 数据月份 */
    private Date dataMonth;
    
    /** 职位ID */
    @TableId
    private String id;
    
    /** 职位编码 */
    private String positionCode;
    
    /** 职位名称 */
    private String title;
    
    /** 职位描述 */
    private String description;
    
    /** 职位要求 */
    private String requirements;
    
    /** 关联部门ID */
    private Long departmentId;
    
    /** 职位级别 */
    private String positionLevel;
    
    /** 编制人数 */
    private Integer headcount;
    
    /** 状态：1-启用，0-禁用 */
    private Integer status;
    
    /** 版本号 */
    @Version
    private Integer version;
    
    /** 逻辑删除标识：0-未删除，1-已删除 */
    @TableLogic
    private Integer deleted;
    
    /** 创建人 */
    private String createBy;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新人 */
    private String updateBy;
    
    /** 更新时间 */
    private Date updateTime;
}
