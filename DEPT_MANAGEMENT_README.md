# 部门管理功能实现说明

## 功能概述

本次实现了一个完整的部门管理功能，支持按租户和月份进行数据管理。该功能包含完整的前后端实现，支持部门的增删改查操作。

## 数据库表结构

```sql
CREATE TABLE departments_monthly (
    tenant_id BIGINT NOT NULL,
    data_month DATE NOT NULL,
    id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识, 0:未删除, 1:已删除',
    parent_department_id BIGINT,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(255),
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (tenant_id, data_month, id)
);
```

## 后端实现

### 1. 实体类
- `DepartmentMonthly.java` - 数据库实体类
- `DepartmentModel.java` - DTO模型类

### 2. 数据访问层
- `DepartmentMapper.java` - MyBatis Mapper接口
- `DepartmentMapper.xml` - SQL映射文件

### 3. 业务服务层
- `IDepartmentService.java` - 服务接口
- `DepartmentServiceImpl.java` - 服务实现类

### 4. 控制器层
- `DepartmentApi.java` - API接口定义
- `DepartmentRestController.java` - REST控制器

### 5. 主要功能
- 部门的增删改查
- 按租户和月份查询部门列表
- 部门代码唯一性验证
- 支持多租户数据隔离

## 前端实现

### 1. 主页面
- `index.vue` - 部门管理主页面
- 支持列表展示、查询、新增、编辑、删除
- 集成月份选择器，支持按月份查看数据

### 2. 编辑组件
- `DeptManagementEdit.vue` - 部门新增/编辑弹窗组件
- 支持表单验证和数据提交

### 3. API调用
- `deptManagement.js` - 前端API调用封装

## 权限配置

系统使用以下权限控制：
- `system_dept_select` - 查询权限
- `system_dept_insert` - 新增权限
- `system_dept_update` - 修改权限
- `system_dept_delete` - 删除权限

## 使用说明

### 1. 访问路径
前端页面路径：`/views/modules/system/deptManagement/`

### 2. API端点
- GET `/api/v1/system/dept/findPage` - 分页查询
- GET `/api/v1/system/dept/findByTenantAndMonth` - 按租户和月份查询
- POST `/api/v1/system/dept/insert` - 新增部门
- POST `/api/v1/system/dept/update` - 修改部门
- POST `/api/v1/system/dept/del` - 删除部门
- GET `/api/v1/system/dept/checkDeptCodeUnique` - 检查代码唯一性

### 3. 主要特性
- **多租户支持**：数据按租户隔离
- **月份管理**：支持按月份查看和管理部门数据
- **代码唯一性**：同一租户同一月份下部门代码唯一
- **树形结构**：支持上下级部门关系
- **状态管理**：支持启用/停用状态

### 4. 注意事项
- 部门代码在同一租户同一月份下必须唯一
- 删除操作为逻辑删除，不会物理删除数据
- 新增部门时会自动设置当前用户的租户ID
- 月份选择器与全局状态同步

## 文件清单

### 后端文件
```
opsli-boot/opsli-modulars/opsli-modulars-system/src/main/java/org/opsli/modulars/system/dept/
├── entity/DepartmentMonthly.java
├── mapper/DepartmentMapper.java
├── mapper/xml/DepartmentMapper.xml
├── service/IDepartmentService.java
├── service/impl/DepartmentServiceImpl.java
└── web/DepartmentRestController.java

opsli-boot/opsli-api/src/main/java/org/opsli/api/
├── web/system/dept/DepartmentApi.java
└── wrapper/system/dept/DepartmentModel.java
```

### 前端文件
```
opsli-ui/vue-admin-better/src/
├── views/modules/system/deptManagement/
│   ├── index.vue
│   └── components/DeptManagementEdit.vue
└── api/system/dept/deptManagement.js
```

## 扩展说明

该实现遵循OPSLI框架的标准模式，可以作为其他模块开发的参考。如需扩展功能，可以：

1. 在实体类中添加新字段
2. 在DTO中添加对应的验证规则
3. 在Service中添加业务逻辑
4. 在Controller中添加新的API端点
5. 在前端添加对应的UI组件

所有代码都遵循框架的编码规范和最佳实践。
