# 考勤管理功能实现说明

## 功能概述

本次实现了一个完整的考勤管理功能，支持按租户和月份进行数据管理。该功能完全遵循员工管理模块的架构模式，包含完整的前后端实现，支持考勤记录的增删改查操作。

## 数据库表结构

```sql
CREATE TABLE `attendances_monthly` (
  `tenant_id` bigint NOT NULL,
  `data_month` date NOT NULL,
  `id` bigint NOT NULL,
  `employee_id` bigint NOT NULL,
  `department_id` bigint NOT NULL,
  `attendance_date` date NOT NULL,
  `check_in_time` time DEFAULT NULL COMMENT '签到时间',
  `check_out_time` time DEFAULT NULL COMMENT '签退时间',
  `status` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '考勤状态: 正常, 迟到, 早退, 旷工, 请假',
  `include_in_stats` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否纳入统计, 1:是, 0:否',
  `remarks` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识, 0:未删除, 1:已删除',
  `version` int DEFAULT 0 COMMENT '乐观锁',
  `create_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`tenant_id`,`data_month`,`id`),
  UNIQUE KEY `uk_attendance_unique` (`tenant_id`,`data_month`,`employee_id`,`attendance_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 后端实现

### 1. 实体类
- `AttendanceMonthly.java` - 数据库实体类
- `AttendanceModel.java` - DTO模型类

### 2. 数据访问层
- `AttendanceMapper.java` - MyBatis Mapper接口
- `AttendanceMapper.xml` - SQL映射文件

### 3. 业务服务层
- `IAttendanceService.java` - 服务接口
- `AttendanceServiceImpl.java` - 服务实现类

### 4. 控制器层
- `AttendanceApi.java` - API接口定义
- `AttendanceRestController.java` - REST控制器

## 前端实现

### 1. 主页面组件
- `index.vue` - 考勤管理主页面，包含列表展示、查询、分页等功能

### 2. 编辑组件
- `AttendanceManagementEdit.vue` - 考勤记录编辑对话框

### 3. API服务
- `attendanceManagement.js` - 前端API调用封装

## 核心功能特性

### 1. 数据管理
- 支持按租户和月份进行数据隔离
- 复合主键设计：(tenant_id, data_month, id)
- 唯一性约束：同一员工在同一天只能有一条考勤记录

### 2. 考勤状态管理
- 正常：按时上下班
- 迟到：超过规定时间签到
- 早退：提前签退
- 旷工：未签到签退
- 请假：已请假

### 3. 查询功能
- 按员工查询
- 按部门查询
- 按考勤状态查询
- 按考勤日期查询
- 支持分页和排序

### 4. 统计功能
- 支持设置是否纳入统计
- 可按状态统计考勤数量
- 支持日期范围查询

## API接口

### 基础CRUD接口
- `GET /api/v1/system/attendance/get` - 获取单条记录
- `GET /api/v1/system/attendance/findPage` - 分页查询
- `GET /api/v1/system/attendance/listAll` - 查询全部
- `POST /api/v1/system/attendance/insert` - 新增记录
- `POST /api/v1/system/attendance/update` - 更新记录
- `POST /api/v1/system/attendance/del` - 删除记录
- `POST /api/v1/system/attendance/delAll` - 批量删除

### 扩展查询接口
- `GET /api/v1/system/attendance/findByEmployeeId` - 按员工ID查询
- `GET /api/v1/system/attendance/findByDepartmentId` - 按部门ID查询
- `GET /api/v1/system/attendance/findByStatus` - 按状态查询

## 权限控制

### 后端权限
- `system_attendance_select` - 查询权限
- `system_attendance_insert` - 新增权限
- `system_attendance_update` - 修改权限
- `system_attendance_delete` - 删除权限

### 前端权限控制
- 按钮级别的权限控制
- 基于用户角色的功能访问控制

## 数据验证

### 后端验证
- 租户ID和数据月份必填
- 员工ID和部门ID必填
- 考勤日期必填
- 考勤状态必填且在预定义范围内
- 备注长度限制（2000字符）

### 前端验证
- 表单字段必填验证
- 日期格式验证
- 时间格式验证
- 实时验证反馈

## 注意事项

### 1. 数据一致性
- 同一员工在同一天只能有一条考勤记录
- 删除操作为逻辑删除，不会物理删除数据
- 使用乐观锁防止并发更新冲突

### 2. 性能优化
- 支持分页查询，避免大数据量加载
- 索引优化，提高查询效率
- 按月份分表设计，便于数据管理

### 3. 用户体验
- 月份选择器与全局状态同步
- 考勤记录与员工、部门数据关联显示
- 状态标签颜色区分，直观显示考勤状态

## 文件清单

### 后端文件
```
opsli-boot/opsli-modulars/opsli-modulars-system/src/main/java/org/opsli/modulars/system/attendance/
├── entity/AttendanceMonthly.java
├── mapper/AttendanceMapper.java
├── mapper/xml/AttendanceMapper.xml
├── service/IAttendanceService.java
├── service/impl/AttendanceServiceImpl.java
└── web/AttendanceRestController.java

opsli-boot/opsli-api/src/main/java/org/opsli/api/
├── web/system/attendance/AttendanceApi.java
└── wrapper/system/attendance/AttendanceModel.java
```

### 前端文件
```
opsli-ui/vue-admin-better/src/
├── views/modules/system/attendanceManagement/
│   ├── index.vue
│   └── components/AttendanceManagementEdit.vue
└── api/system/attendance/attendanceManagement.js
```

## 扩展说明

该实现完全遵循OPSLI框架的标准模式和员工管理模块的架构，可以作为其他模块开发的参考。如需扩展功能，可以：

1. 添加考勤规则配置
2. 实现考勤统计报表
3. 集成打卡设备接口
4. 添加考勤异常提醒
5. 实现考勤数据导入导出

## 技术栈

- **后端**: Spring Boot + MyBatis Plus + MySQL
- **前端**: Vue.js + Element UI
- **架构**: 前后端分离 + RESTful API
- **数据库**: MySQL 8.0+
- **权限**: Spring Security + 自定义权限注解
