2025-07-21 09:43:56:371 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-07-21 09:43:56:373 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-07-21 09:43:56:374 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-21 09:43:56:374 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-07-21 09:43:56:375 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753062236357.0918"]
}
2025-07-21 09:43:56:426 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 55ms -------------
2025-07-21 09:43:56:440 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-07-21 09:43:56:440 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-07-21 09:43:56:440 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: OPTIONS
2025-07-21 09:43:56:440 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 0ms -------------
2025-07-21 09:43:56:443 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-07-21 09:43:56:444 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-07-21 09:43:56:444 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-21 09:43:56:444 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-07-21 09:43:59:317 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 2874ms -------------
2025-07-21 09:43:59:439 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-21 09:43:59:440 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-21 09:43:59:440 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-21 09:43:59:440 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-21 09:43:59:601 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 162ms -------------
2025-07-21 09:43:59:717 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-21 09:43:59:717 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-21 09:43:59:717 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-21 09:43:59:717 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
