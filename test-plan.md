# Department Management Test Plan

## Overview
This test plan outlines the steps to verify the complete workflow of the department management feature, from UI to database.

## Prerequisites
1. OPSLI backend server is running
2. Database is properly configured
3. Frontend application is running
4. User has appropriate permissions for department management

## Test Cases

### 1. Basic UI Rendering
- **Objective**: Verify that the department management page loads correctly
- **Steps**:
  1. Navigate to the department management page
  2. Verify that the page title, search form, and data table are displayed
  3. Verify that the month picker is integrated and shows the current month
- **Expected Result**: All UI elements are properly rendered

### 2. Permission-Based UI Rendering
- **Objective**: Verify that UI elements are shown/hidden based on user permissions
- **Steps**:
  1. Log in as a user with all department management permissions
  2. Verify that all action buttons (Add, Edit, Delete) are visible
  3. Log in as a user with read-only permissions
  4. Verify that action buttons are hidden
- **Expected Result**: UI elements are shown/hidden based on user permissions

### 3. Department Listing
- **Objective**: Verify that departments are correctly listed
- **Steps**:
  1. Navigate to the department management page
  2. Verify that departments are displayed in the table
  3. Verify that pagination works correctly
  4. Verify that the table shows correct department information
- **Expected Result**: Departments are correctly listed with pagination

### 4. Department Filtering
- **Objective**: Verify that department filtering works correctly
- **Steps**:
  1. Enter a department name in the search field
  2. Click the search button
  3. Verify that only matching departments are displayed
  4. Clear the search field and select a status filter
  5. Verify that only departments with the selected status are displayed
- **Expected Result**: Filtering works correctly for all search criteria

### 5. Month-Based Filtering
- **Objective**: Verify that month-based filtering works correctly
- **Steps**:
  1. Select a different month in the month picker
  2. Verify that the department list is updated to show data for the selected month
- **Expected Result**: Department list is filtered by the selected month

### 6. Department Creation
- **Objective**: Verify that new departments can be created
- **Steps**:
  1. Click the "Add" button
  2. Fill in the department form with valid data
  3. Click "Save"
  4. Verify that the new department appears in the list
  5. Verify that the department is saved in the database
- **Expected Result**: New department is created and displayed in the list

### 7. Department Editing
- **Objective**: Verify that departments can be edited
- **Steps**:
  1. Click the "Edit" button for an existing department
  2. Modify department information
  3. Click "Save"
  4. Verify that the department information is updated in the list
  5. Verify that the department is updated in the database
- **Expected Result**: Department information is updated

### 8. Department Deletion
- **Objective**: Verify that departments can be deleted
- **Steps**:
  1. Click the "Delete" button for an existing department
  2. Confirm deletion
  3. Verify that the department is removed from the list
  4. Verify that the department is marked as deleted in the database
- **Expected Result**: Department is deleted

### 9. Parent-Child Relationship
- **Objective**: Verify that parent-child relationships work correctly
- **Steps**:
  1. Create a parent department
  2. Create a child department by selecting the parent department
  3. Verify that the child department shows the correct parent department name
  4. Try to set a child department as the parent of its own parent
  5. Verify that validation prevents circular references
- **Expected Result**: Parent-child relationships work correctly with validation

### 10. Form Validation
- **Objective**: Verify that form validation works correctly
- **Steps**:
  1. Try to submit the form with empty required fields
  2. Verify that validation errors are displayed
  3. Try to create a department with a duplicate name
  4. Verify that a uniqueness validation error is displayed
  5. Try to create a department with a duplicate code
  6. Verify that a uniqueness validation error is displayed
- **Expected Result**: Form validation works correctly for all validation rules

### 11. Multi-Tenant Data Isolation
- **Objective**: Verify that data is properly isolated between tenants
- **Steps**:
  1. Log in as a user from Tenant A
  2. Create a department for Tenant A
  3. Log in as a user from Tenant B
  4. Verify that the department created for Tenant A is not visible
- **Expected Result**: Data is properly isolated between tenants

## Test Execution Checklist

| Test Case | Status | Notes |
|-----------|--------|-------|
| 1. Basic UI Rendering | | |
| 2. Permission-Based UI Rendering | | |
| 3. Department Listing | | |
| 4. Department Filtering | | |
| 5. Month-Based Filtering | | |
| 6. Department Creation | | |
| 7. Department Editing | | |
| 8. Department Deletion | | |
| 9. Parent-Child Relationship | | |
| 10. Form Validation | | |
| 11. Multi-Tenant Data Isolation | | |

## Issues and Observations
- [Document any issues or observations here]