# 职位管理功能实现说明

## 功能概述

本次实现了一个完整的职位管理功能，支持按租户和月份进行数据管理。该功能完全遵循部门管理的架构模式和代码规范，包含完整的前后端实现，支持职位的增删改查操作。

## 数据库表结构

```sql
CREATE TABLE positions_monthly (
    tenant_id BIGINT NOT NULL,
    data_month DATE NOT NULL,
    id BIGINT NOT NULL,
    position_code VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    requirements TEXT,
    department_id BIGINT,
    position_level VARCHAR(50),
    headcount INT,
    status TINYINT(1) DEFAULT 1,
    version INT DEFAULT 0,
    deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识, 0:未删除, 1:已删除',
    create_by VARCHAR(255),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(255),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (tenant_id, data_month, id),
    UNIQUE KEY uk_tenant_month_code (tenant_id, data_month, position_code, deleted),
    INDEX idx_department (tenant_id, data_month, department_id)
);
```

## 后端实现

### 1. 实体类
- `PositionMonthly.java` - 数据库实体类
- `PositionModel.java` - DTO模型类

### 2. 数据访问层
- `PositionMapper.java` - MyBatis Mapper接口
- `PositionMapper.xml` - SQL映射文件

### 3. 业务服务层
- `IPositionService.java` - 服务接口
- `PositionServiceImpl.java` - 服务实现类

### 4. 控制器层
- `PositionApi.java` - API接口定义
- `PositionRestController.java` - REST控制器

### 5. 主要功能
- 职位的增删改查
- 按租户和月份查询职位列表
- 职位编码和名称唯一性验证
- 支持多租户数据隔离
- 与部门的关联关系管理

## 前端实现

### 1. 主页面
- `index.vue` - 职位管理主页面
- 支持列表展示、查询、新增、编辑、删除
- 集成月份选择器，支持按月份查看数据
- 支持按部门筛选职位

### 2. 编辑组件
- `PosManagementEdit.vue` - 职位新增/编辑弹窗组件
- 支持表单验证和数据提交
- 部门选择下拉框

### 3. API调用
- `posManagement.js` - 前端API调用封装

## 权限配置

系统使用以下权限控制：
- `system_position_select` - 查询权限
- `system_position_insert` - 新增权限
- `system_position_update` - 修改权限
- `system_position_delete` - 删除权限

## 使用说明

### 1. 访问路径
前端页面路径：`/views/modules/system/posManagement/`

### 2. API端点
- GET `/api/v1/system/position/findPage` - 分页查询
- GET `/api/v1/system/position/listAll` - 查询所有数据
- POST `/api/v1/system/position/insert` - 新增职位
- POST `/api/v1/system/position/update` - 修改职位
- POST `/api/v1/system/position/del` - 删除职位
- GET `/api/v1/system/position/findByDepartmentId` - 按部门查询职位
- POST `/api/v1/system/position/checkCodeUnique` - 检查编码唯一性
- POST `/api/v1/system/position/checkTitleUnique` - 检查名称唯一性

### 3. 主要特性
- **多租户支持**：数据按租户隔离
- **月份管理**：支持按月份查看和管理职位数据
- **编码唯一性**：同一租户同一月份下职位编码唯一
- **名称唯一性**：同一租户同一月份下职位名称唯一
- **部门关联**：职位与部门的关联关系
- **状态管理**：支持启用/停用状态
- **编制管理**：支持设置职位编制人数

### 4. 注意事项
- 职位编码和名称在同一租户同一月份下必须唯一
- 删除操作为逻辑删除，不会物理删除数据
- 新增职位时会自动设置当前用户的租户ID
- 月份选择器与全局状态同步
- 职位与部门存在关联关系，可按部门筛选职位

## 文件清单

### 后端文件
```
opsli-boot/opsli-modulars/opsli-modulars-system/src/main/java/org/opsli/modulars/system/position/
├── entity/PositionMonthly.java
├── mapper/PositionMapper.java
├── mapper/xml/PositionMapper.xml
├── service/IPositionService.java
├── service/impl/PositionServiceImpl.java
└── web/PositionRestController.java

opsli-boot/opsli-api/src/main/java/org/opsli/api/
├── web/system/position/PositionApi.java
└── wrapper/system/position/PositionModel.java
```

### 前端文件
```
opsli-ui/vue-admin-better/src/
├── views/modules/system/posManagement/
│   ├── index.vue
│   └── components/PosManagementEdit.vue
└── api/system/position/posManagement.js
```

## 架构特点

该实现严格遵循OPSLI框架的标准模式和部门管理的架构设计：

1. **分层架构**：API层、Web层、Service层、Mapper层清晰分离
2. **统一规范**：命名规范、代码结构与部门管理保持一致
3. **权限控制**：集成OPSLI权限体系
4. **多租户支持**：完整的多租户数据隔离
5. **月份管理**：支持按月份进行数据管理
6. **前后端分离**：Vue.js前端与Spring Boot后端分离
7. **数据验证**：前后端双重数据验证
8. **错误处理**：统一的错误处理机制

## 扩展说明

该实现可以作为其他模块开发的参考模板。如需扩展功能，可以：

1. 添加更多的职位属性字段
2. 实现职位层级关系
3. 添加职位与员工的关联
4. 实现职位模板功能
5. 添加职位统计报表

所有扩展都应遵循现有的架构模式和代码规范。
