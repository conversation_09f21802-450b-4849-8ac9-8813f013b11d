-- Department Management Menu Setup SQL

-- 1. Add Department Management Menu
INSERT INTO sys_menu (id, parent_id, menu_name, menu_type, menu_icon, menu_url, menu_order, menu_visible, menu_perms, menu_open_type, iz_lock, remark, create_by, create_time, update_by, update_time, deleted)
VALUES (
    -- Generate a unique ID based on your system's ID generation strategy
    (SELECT MAX(id) + 1 FROM sys_menu),
    -- Parent ID (System Management menu ID, adjust as needed)
    (SELECT id FROM sys_menu WHERE menu_name = '系统管理' AND menu_type = '0' AND deleted = 0 LIMIT 1),
    '部门管理',
    '1', -- Menu type: 1 for menu
    'el-icon-office-building', -- Icon
    '/system/deptManagement', -- URL path
    -- Order (adjust as needed)
    (SELECT COALESCE(MAX(menu_order), 0) + 10 FROM sys_menu WHERE parent_id = (SELECT id FROM sys_menu WHERE menu_name = '系统管理' AND menu_type = '0' AND deleted = 0 LIMIT 1)),
    '0', -- Visible: 0 for visible
    'system_department', -- Permission prefix
    '0', -- Open type: 0 for current window
    '0', -- Lock: 0 for not locked
    '部门管理模块',
    'admin', -- Create by
    NOW(), -- Create time
    'admin', -- Update by
    NOW(), -- Update time
    '0' -- Deleted: 0 for not deleted
);

-- 2. Add Department Management Permissions
-- Get the menu ID we just inserted
SET @menu_id = (SELECT MAX(id) FROM sys_menu WHERE menu_name = '部门管理' AND deleted = 0);

-- 2.1 Add View Permission
INSERT INTO sys_menu (id, parent_id, menu_name, menu_type, menu_perms, menu_order, menu_visible, iz_lock, create_by, create_time, update_by, update_time, deleted)
VALUES (
    (SELECT MAX(id) + 1 FROM sys_menu),
    @menu_id,
    '查看',
    '2', -- Menu type: 2 for button
    'system_department_select',
    10,
    '0', -- Visible: 0 for visible
    '0', -- Lock: 0 for not locked
    'admin',
    NOW(),
    'admin',
    NOW(),
    '0'
);

-- 2.2 Add Add Permission
INSERT INTO sys_menu (id, parent_id, menu_name, menu_type, menu_perms, menu_order, menu_visible, iz_lock, create_by, create_time, update_by, update_time, deleted)
VALUES (
    (SELECT MAX(id) + 1 FROM sys_menu),
    @menu_id,
    '新增',
    '2', -- Menu type: 2 for button
    'system_department_insert',
    20,
    '0', -- Visible: 0 for visible
    '0', -- Lock: 0 for not locked
    'admin',
    NOW(),
    'admin',
    NOW(),
    '0'
);

-- 2.3 Add Edit Permission
INSERT INTO sys_menu (id, parent_id, menu_name, menu_type, menu_perms, menu_order, menu_visible, iz_lock, create_by, create_time, update_by, update_time, deleted)
VALUES (
    (SELECT MAX(id) + 1 FROM sys_menu),
    @menu_id,
    '修改',
    '2', -- Menu type: 2 for button
    'system_department_update',
    30,
    '0', -- Visible: 0 for visible
    '0', -- Lock: 0 for not locked
    'admin',
    NOW(),
    'admin',
    NOW(),
    '0'
);

-- 2.4 Add Delete Permission
INSERT INTO sys_menu (id, parent_id, menu_name, menu_type, menu_perms, menu_order, menu_visible, iz_lock, create_by, create_time, update_by, update_time, deleted)
VALUES (
    (SELECT MAX(id) + 1 FROM sys_menu),
    @menu_id,
    '删除',
    '2', -- Menu type: 2 for button
    'system_department_delete',
    40,
    '0', -- Visible: 0 for visible
    '0', -- Lock: 0 for not locked
    'admin',
    NOW(),
    'admin',
    NOW(),
    '0'
);

-- 3. Assign Permissions to Admin Role
-- Get the admin role ID
SET @admin_role_id = (SELECT id FROM sys_role WHERE role_code = 'admin' AND deleted = 0 LIMIT 1);

-- Insert role-menu relations for all department management permissions
INSERT INTO sys_role_menu (id, role_id, menu_id, create_by, create_time, update_by, update_time)
SELECT 
    (SELECT COALESCE(MAX(id), 0) + ROW_NUMBER() OVER () FROM sys_role_menu),
    @admin_role_id,
    id,
    'admin',
    NOW(),
    'admin',
    NOW()
FROM sys_menu 
WHERE (id = @menu_id OR parent_id = @menu_id) AND deleted = 0;

-- 4. Clear Cache (This will be handled by the application)
-- Note: After running this script, you may need to clear the application cache
-- or restart the application for the changes to take effect.