# Project Structure & Organization

## Root Structure
```
├── opsli-boot/          # Backend Spring Boot application
├── opsli-ui/            # Frontend Vue.js application  
└── logs/                # Application logs
```

## Backend Structure (opsli-boot/)

### Core Modules
- **opsli-api/**: External API controllers and web layer
- **opsli-base-support/**: Foundation modules
  - `opsli-common/`: Common utilities, constants, exceptions
  - `opsli-core/`: Core framework components (security, cache, filters)
- **opsli-modulars/**: Business modules
  - `opsli-modulars-system/`: System management (users, roles, menus, etc.)
  - `opsli-modulars-generator/`: Code generation functionality
  - `opsli-modulars-test/`: Test modules
- **opsli-plugins/**: Plugin system
  - `opsli-plugins-redis/`: Redis caching plugin
  - `opsli-plugins-security/`: Security authentication plugin
  - `opsli-plugins-crypto/`: Encryption/decryption plugin
  - `opsli-plugins-email/`: Email service plugin
  - `opsli-plugins-excel/`: Excel processing plugin
  - `opsli-plugins-sms/`: SMS service plugin
  - `opsli-plugins-waf/`: Web Application Firewall plugin
- **opsli-starter/**: Application entry point and configuration

### Package Conventions
- **Base package**: `org.opsli`
- **API layer**: `org.opsli.api.web`
- **Business layer**: `org.opsli.modulars.{domain}`
- **Core layer**: `org.opsli.core`
- **Plugin layer**: `org.opsli.plugins.{plugin-name}`

### Standard Module Structure
```
src/main/java/org/opsli/modulars/{domain}/
├── entity/              # Database entities
├── mapper/              # MyBatis mappers
│   └── xml/            # MyBatis XML files
├── service/            # Business service interfaces
│   └── impl/           # Service implementations
├── web/                # REST controllers
├── dto/                # Data Transfer Objects
└── vo/                 # View Objects
```

## Frontend Structure (opsli-ui/vue-admin-better/)

### Core Directories
- **src/api/**: API service definitions
- **src/components/**: Reusable Vue components
- **src/views/**: Page components
  - `modules/system/`: System management pages
  - `modules/generator/`: Code generator pages
- **src/router/**: Vue Router configuration
- **src/store/**: Vuex state management
- **src/utils/**: Utility functions
- **src/styles/**: Global styles and themes
- **public/**: Static assets

### Component Organization
- **Global components**: `src/components/`
- **Business components**: `src/components/opsli/`
- **Page views**: `src/views/modules/{domain}/`
- **API services**: `src/api/{domain}/`

## Configuration Structure

### Backend Configuration
- **Main config**: `opsli-starter/src/main/resources/application.yaml`
- **Environment configs**: `application-{env}.yaml`
- **Database scripts**: `db-file/{version}/`
- **Templates**: Code generation templates in respective modules

### Frontend Configuration  
- **Build config**: `vue.config.js`
- **Dependencies**: `package.json`
- **Environment**: `.env` files
- **Routing**: `src/router/`

## Naming Conventions

### Backend
- **Classes**: PascalCase (e.g., `UserService`, `MenuController`)
- **Methods**: camelCase (e.g., `getUserById`, `createMenu`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `DEFAULT_PASSWORD`)
- **Packages**: lowercase with dots (e.g., `org.opsli.modulars.system`)

### Frontend
- **Components**: PascalCase (e.g., `UserList.vue`, `MenuTree.vue`)
- **Files**: kebab-case (e.g., `user-management.js`)
- **Variables**: camelCase (e.g., `userInfo`, `menuList`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_BASE_URL`)

## Key Architectural Patterns
- **Plugin Architecture**: Hot-pluggable modules in `opsli-plugins/`
- **Multi-module Maven**: Separate concerns across modules
- **Layered Architecture**: API → Service → Mapper → Entity
- **Multi-tenant**: Tenant isolation at data and business layers
- **Code Generation**: Template-based code generation system