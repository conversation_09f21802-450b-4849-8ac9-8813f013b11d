# Technology Stack & Build System

## Backend Stack
- **Java**: <PERSON><PERSON><PERSON> 22
- **Framework**: Spring Boot 3.4.6
- **ORM**: MyBatis-Plus 3.5.12
- **Database**: MySQL with Druid 1.2.25 connection pool
- **Cache**: <PERSON><PERSON> + <PERSON><PERSON><PERSON><PERSON> (L1/L2 cache)
- **Security**: Spring Security + JWT 4.5.0
- **Documentation**: SpringDoc OpenAPI 2.8.8
- **Template Engine**: Jfinal Enjoy 5.2.2 (for code generation)
- **Utilities**: Hutool 5.8.38, Guava 33.4.8-jre
- **JSON**: Fastjson 1.2.83

## Frontend Stack
- **Framework**: Vue.js 2.6.14
- **UI Library**: Element UI 2.15.13
- **Admin Template**: vue-admin-beautiful
- **State Management**: Vuex 3.6.2
- **Routing**: Vue Router 3.5.3
- **HTTP Client**: Axios 1.3.4
- **Build Tool**: Vue CLI 4.5.15
- **Charts**: ECharts 5.4.1

## Build System

### Backend (Maven)
```bash
# Development
mvn clean compile
mvn spring-boot:run

# Build for different environments
mvn clean package -P local    # Local environment
mvn clean package -P dev      # Development environment  
mvn clean package -P test     # Test environment
mvn clean package -P prod     # Production environment

# Run tests
mvn test

# Generate documentation
mvn javadoc:javadoc
```

### Frontend (npm/yarn)
```bash
# Install dependencies
npm install
# or
yarn install

# Development server
npm run serve
# or  
yarn serve

# Production build
npm run build
# or
yarn build

# Lint and fix
npm run lint
# or
yarn lint
```

## Docker Support
```bash
# Build and run with Docker Compose
docker-compose up -d

# Build individual services
docker build -t opsli-boot ./opsli-boot
docker build -t opsli-ui ./opsli-ui
```

## Key Configuration Files
- **Backend**: `application.yaml`, `application-{env}.yaml`
- **Frontend**: `vue.config.js`, `package.json`
- **Database**: SQL files in `db-file/` directory
- **Docker**: `docker-compose.yml`, `Dockerfile`

## Development Profiles
- `local`: Local development (default)
- `dev`: Development environment
- `test`: Testing environment  
- `prod`: Production environment