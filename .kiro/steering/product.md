# OPSLI Product Overview

OPSLI is a low-code rapid development platform designed for building backend management systems with zero-code development capabilities.

## Core Features
- **Low-code/Zero-code Development**: Visual code generation with customizable templates
- **Multi-tenant SaaS**: Complete multi-tenant architecture with tenant isolation
- **Security-first**: Advanced authentication, data encryption, and WAF protection
- **Plugin Architecture**: Hot-pluggable business modules and extensible plugins
- **Code Generator**: Built-in code generator using Jfinal Enjoy template engine
- **API Management**: Multi-version API support (v1-vn) with terminal compatibility

## Architecture
- **Frontend**: Vue.js 2.6 + Element UI + vue-admin-beautiful
- **Backend**: Spring Boot 3.4.6 + MyBatis-Plus + Redis
- **Database**: MySQL with Druid connection pool
- **Security**: Spring Security with JWT authentication
- **Caching**: Redis + Caffeine (L1/L2 cache)

## Key Business Domains
- User & Role Management
- Organization & Tenant Management  
- Menu & Permission System
- System Monitoring & Logging
- Code Generation Tools
- File Storage (OSS integration)

## Target Use Cases
- Enterprise management systems
- SaaS platform development
- Rapid prototyping
- Backend system scaffolding