# Design Document

## Overview

The department management feature will be implemented following OPSLI's standard multi-layered architecture pattern. The system will provide a complete department management solution with multi-tenant support and monthly data segmentation. The implementation will include both backend REST APIs and a Vue.js frontend interface.

## Architecture

### Backend Architecture

The backend follows OPSLI's standard layered architecture:

```
API Layer (opsli-api)
├── DepartmentApi.java (Interface definition)
└── DepartmentModel.java (DTO/Wrapper)

Web Layer (opsli-modulars-system)
└── DepartmentRestController.java (REST endpoints)

Service Layer
├── IDepartmentService.java (Service interface)
└── DepartmentServiceImpl.java (Business logic)

Data Access Layer
├── DepartmentMapper.java (MyBatis interface)
├── DepartmentMapper.xml (SQL mappings)
└── DepartmentMonthly.java (Entity)
```

### Frontend Architecture

The frontend follows Vue.js component-based architecture:

```
Views Layer
├── index.vue (Main department management page)
└── components/DeptManagementEdit.vue (Add/Edit dialog)

API Layer
└── deptManagement.js (API service calls)

State Management
└── Integration with global month picker state
```

## Components and Interfaces

### Backend Components

#### 1. Entity Layer
- **DepartmentMonthly**: JPA entity representing the departments_monthly table
  - Composite primary key: (tenant_id, data_month, id)
  - Logical deletion support with 'deleted' flag
  - Audit fields: created_by, created_time, updated_by, updated_time
  - Parent-child relationship support

#### 2. DTO Layer
- **DepartmentModel**: Data transfer object for API communication
  - Validation annotations for required fields
  - Serialization support for JSON responses
  - Mapping utilities for entity conversion

#### 3. Service Layer
- **IDepartmentService**: Service interface defining business operations
- **DepartmentServiceImpl**: Implementation with business logic
  - Multi-tenant data filtering
  - Department code uniqueness validation
  - Hierarchical relationship management
  - Logical deletion handling

#### 4. Web Layer
- **DepartmentApi**: API interface definition with Swagger documentation
- **DepartmentRestController**: REST controller implementation
  - Standard CRUD endpoints
  - Pagination support
  - Permission-based access control

### Frontend Components

#### 1. Main Page Component (index.vue)
- Department list display with table layout
- Search and filter functionality
- Month picker integration
- Action buttons (Add, Edit, Delete)
- Permission-based UI rendering

#### 2. Edit Dialog Component (DeptManagementEdit.vue)
- Form-based department creation/editing
- Parent department selection
- Real-time validation
- Submit/Cancel actions

#### 3. API Service (deptManagement.js)
- HTTP client wrapper for backend APIs
- Request/response transformation
- Error handling

## Data Models

### Database Schema

```sql
CREATE TABLE departments_monthly (
    tenant_id BIGINT NOT NULL,
    data_month DATE NOT NULL,
    id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    dept_code VARCHAR(100) NOT NULL,
    parent_department_id BIGINT,
    status TINYINT(1) DEFAULT 1,
    version INT DEFAULT 0,
    resoibsible_person BIGINT,
    responsibilities TEXT,
    deleted TINYINT(1) NOT NULL DEFAULT 0,
    create_by VARCHAR(255),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(255),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (tenant_id, data_month, id),
    UNIQUE KEY uk_tenant_month_code (tenant_id, data_month, code, deleted),
    INDEX idx_parent_dept (tenant_id, data_month, parent_department_id)
);
```

### API Data Models

#### DepartmentModel
```java
public class DepartmentModel {
    private Long tenantId;
    private Date dataMonth;
    private Long id;
    private String name;
    private String code;
    private Long parentDepartmentId;
    private String parentDepartmentName;
    private Long responsiblePersonId;
    private Integer status;
    private Integer version;
    private String remark;
    // Audit fields
    private String createdBy;
    private Date createdTime;
    private String updatedBy;
    private Date updatedTime;
}
```

## Error Handling

### Backend Error Handling
- Custom exceptions for business logic violations
- Global exception handler for consistent error responses
- Validation error mapping for form field errors
- Database constraint violation handling

### Frontend Error Handling
- API error response processing
- User-friendly error message display
- Form validation error highlighting
- Network error handling with retry options

## Testing Strategy

### Backend Testing
1. **Unit Tests**
   - Service layer business logic testing
   - Mapper layer database operation testing
   - Validation logic testing

2. **Integration Tests**
   - Controller endpoint testing
   - Database transaction testing
   - Multi-tenant data isolation testing

3. **API Tests**
   - REST endpoint functionality testing
   - Permission-based access testing
   - Data validation testing

### Frontend Testing
1. **Component Tests**
   - Vue component rendering testing
   - User interaction testing
   - Form validation testing

2. **Integration Tests**
   - API integration testing
   - State management testing
   - Navigation flow testing

3. **E2E Tests**
   - Complete user workflow testing
   - Cross-browser compatibility testing
   - Responsive design testing

## Security Considerations

### Authentication & Authorization
- JWT token-based authentication
- Role-based permission checking
- Multi-tenant data isolation
- API endpoint protection

### Data Security
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF token validation

### Audit Trail
- User action logging
- Data change tracking
- Access attempt monitoring
- Error event logging

## Performance Considerations

### Backend Performance
- Database query optimization with proper indexing
- Pagination for large datasets
- Caching strategy for frequently accessed data
- Connection pool optimization

### Frontend Performance
- Component lazy loading
- API response caching
- Virtual scrolling for large lists
- Debounced search functionality

## Integration Points

### System Integration
- Integration with OPSLI's user management system
- Integration with tenant management system
- Integration with permission system
- Integration with audit logging system

### External Integration
- Month picker component integration
- Global state management integration
- Theme and styling integration
- Internationalization support