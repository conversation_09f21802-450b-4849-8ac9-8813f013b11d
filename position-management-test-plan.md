# 职位管理功能测试计划

## 测试概述

本测试计划用于验证职位管理功能的完整性和正确性，确保其与部门管理功能保持一致的架构模式和用户体验。

## 测试环境准备

### 1. 数据库准备
确保 `positions_monthly` 表已创建，包含以下字段：
- tenant_id (租户ID)
- data_month (数据月份)
- id (主键)
- position_code (职位编码)
- title (职位名称)
- description (职位描述)
- requirements (职位要求)
- department_id (关联部门ID)
- position_level (职位级别)
- headcount (编制人数)
- status (状态)
- version (版本号)
- deleted (逻辑删除标识)
- create_by, create_time, update_by, update_time (审计字段)

### 2. 权限配置
确保以下权限已配置：
- system_position_select
- system_position_insert
- system_position_update
- system_position_delete

### 3. 菜单配置
添加职位管理菜单项到系统管理模块下。

## 后端API测试

### 1. 基础CRUD操作测试

#### 1.1 新增职位 (POST /api/v1/system/position/insert)
**测试数据：**
```json
{
  "tenantId": 1,
  "dataMonth": "2025-07",
  "positionCode": "DEV001",
  "title": "高级Java开发工程师",
  "description": "负责Java后端开发工作",
  "requirements": "3年以上Java开发经验",
  "departmentId": 1,
  "positionLevel": "P6",
  "headcount": 5,
  "status": 1
}
```

**预期结果：**
- 返回成功消息
- 数据库中新增一条记录
- 自动设置创建时间和创建人

#### 1.2 查询职位列表 (GET /api/v1/system/position/findPage)
**测试参数：**
- pageNo: 1
- pageSize: 10
- tenantId: 1
- dataMonth: 2025-07

**预期结果：**
- 返回分页数据
- 包含总数和记录列表
- 数据按租户和月份过滤

#### 1.3 修改职位 (POST /api/v1/system/position/update)
**测试数据：**
```json
{
  "id": "生成的ID",
  "tenantId": 1,
  "dataMonth": "2025-07",
  "positionCode": "DEV001",
  "title": "资深Java开发工程师",
  "description": "负责Java后端开发和架构设计",
  "requirements": "5年以上Java开发经验",
  "departmentId": 1,
  "positionLevel": "P7",
  "headcount": 3,
  "status": 1
}
```

**预期结果：**
- 返回成功消息
- 数据库记录被更新
- 自动更新修改时间和修改人

#### 1.4 删除职位 (POST /api/v1/system/position/del)
**测试参数：**
- id: 职位ID

**预期结果：**
- 返回成功消息
- 记录被逻辑删除（deleted=1）
- 物理记录仍存在

### 2. 业务逻辑测试

#### 2.1 唯一性验证测试
**职位编码唯一性：**
- 在同一租户同一月份下创建相同编码的职位
- 预期：返回编码重复错误

**职位名称唯一性：**
- 在同一租户同一月份下创建相同名称的职位
- 预期：返回名称重复错误

#### 2.2 部门关联测试
**按部门查询职位：**
- 调用 `/api/v1/system/position/findByDepartmentId`
- 传入部门ID
- 预期：返回该部门下的所有职位

#### 2.3 多租户隔离测试
- 使用不同租户ID创建职位
- 查询时验证只能看到本租户的数据
- 预期：数据完全隔离

#### 2.4 月份管理测试
- 在不同月份创建职位
- 切换月份查询
- 预期：只显示当前月份的数据

## 前端功能测试

### 1. 页面加载测试
- 访问职位管理页面
- 验证页面正常加载
- 验证表格、搜索框、按钮等组件显示正常

### 2. 数据展示测试
- 验证职位列表正确显示
- 验证分页功能正常
- 验证部门名称正确显示（关联查询）

### 3. 搜索功能测试
- 按职位名称搜索
- 按职位编码搜索
- 按部门筛选
- 按状态筛选
- 验证搜索结果正确

### 4. 新增功能测试
- 点击"添加"按钮
- 填写职位信息
- 验证表单验证规则
- 提交并验证成功

### 5. 编辑功能测试
- 点击"编辑"按钮
- 修改职位信息
- 验证数据回显正确
- 提交并验证更新成功

### 6. 删除功能测试
- 点击"删除"按钮
- 确认删除操作
- 验证记录被删除

### 7. 状态切换测试
- 使用状态开关切换职位状态
- 验证状态更新成功
- 验证确认对话框正常显示

## 集成测试

### 1. 与部门管理的集成
- 创建部门后在职位管理中选择该部门
- 删除部门时验证关联职位的处理
- 验证部门名称在职位列表中正确显示

### 2. 权限集成测试
- 使用不同权限的用户登录
- 验证按钮和功能的显示/隐藏
- 验证API调用的权限控制

### 3. 月份选择器集成
- 切换全局月份选择器
- 验证职位数据自动刷新
- 验证与部门管理的月份同步

## 性能测试

### 1. 数据量测试
- 创建大量职位数据（1000+条）
- 测试分页查询性能
- 测试搜索功能性能

### 2. 并发测试
- 多用户同时操作职位管理
- 验证数据一致性
- 验证系统稳定性

## 兼容性测试

### 1. 浏览器兼容性
- Chrome
- Firefox
- Safari
- Edge

### 2. 响应式测试
- 桌面端显示
- 平板端显示
- 手机端显示

## 测试验收标准

### 1. 功能完整性
- [ ] 所有CRUD操作正常
- [ ] 搜索和筛选功能正常
- [ ] 数据验证规则正确
- [ ] 权限控制有效

### 2. 用户体验
- [ ] 界面美观，与系统风格一致
- [ ] 操作流程顺畅
- [ ] 错误提示友好
- [ ] 响应速度快

### 3. 数据安全
- [ ] 多租户数据隔离
- [ ] 逻辑删除正确
- [ ] 审计字段完整
- [ ] 权限控制严格

### 4. 系统稳定性
- [ ] 无内存泄漏
- [ ] 无死锁问题
- [ ] 异常处理完善
- [ ] 日志记录完整

## 测试报告

测试完成后，需要提供包含以下内容的测试报告：
1. 测试执行情况汇总
2. 发现的问题及修复情况
3. 性能测试结果
4. 建议和改进意见
5. 最终验收结论
