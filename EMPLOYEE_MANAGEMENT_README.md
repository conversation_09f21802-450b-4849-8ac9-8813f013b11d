# 员工管理功能实现说明

## 功能概述

本次实现了一个完整的员工管理功能，支持按租户和月份进行数据管理。该功能包含完整的前后端实现，支持员工的增删改查操作，严格遵循Position Management的架构模式。

## 数据库表结构

```sql
CREATE TABLE `employees_monthly` (
  `tenant_id` bigint NOT NULL,
  `data_month` date NOT NULL,
  `id` bigint NOT NULL,
  `employee_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `gender` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '性别',
  `date_of_birth` date DEFAULT NULL COMMENT '出生日期',
  `phone_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码',
  `id_card_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `department_id` bigint NOT NULL,
  `position_id` bigint NOT NULL,
  `concurrent_position_id` bigint DEFAULT NULL COMMENT '兼岗位ID',
  `hire_date` date DEFAULT NULL,
  `status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '员工状态: 在职, 离职, 试用期',
  `has_social_insurance` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否缴纳社保, 1:是, 0:否',
  `is_contract_signed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否签订合同, 1:是, 0:否',
  `is_probation_passed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已转正, 1:是, 0:否',
  `are_documents_signed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否签订资料, 1:是, 0:否',
  `personal_bio` text COLLATE utf8mb4_unicode_ci COMMENT '个人简介',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识, 0:未删除, 1:已删除',
  `version` int DEFAULT 0 COMMENT '乐观锁',
  `create_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`tenant_id`,`data_month`,`id`),
  UNIQUE KEY `uk_employee_number` (`tenant_id`,`data_month`,`employee_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 后端实现

### 1. 实体类
- `EmployeeMonthly.java` - 数据库实体类
- `EmployeeModel.java` - DTO模型类

### 2. 数据访问层
- `EmployeeMapper.java` - MyBatis Mapper接口
- `EmployeeMapper.xml` - SQL映射文件

### 3. 业务服务层
- `IEmployeeService.java` - 服务接口
- `EmployeeServiceImpl.java` - 服务实现类

### 4. 控制器层
- `EmployeeApi.java` - API接口定义
- `EmployeeRestController.java` - REST控制器

### 5. 主要功能
- 员工的增删改查
- 按租户和月份查询员工列表
- 员工编号和身份证号唯一性验证
- 支持多租户数据隔离
- 与部门和职位的关联关系管理
- 根据部门ID或职位ID查询员工列表

## 前端实现

### 1. 主页面
- `index.vue` - 员工管理主页面
- 支持列表展示、查询、新增、编辑、删除
- 集成月份选择器，支持按月份查看数据
- 支持按部门、职位、状态筛选员工
- 显示员工基本信息和管理状态（社保、合同、转正、资料）

### 2. 编辑组件
- `EmployeeManagementEdit.vue` - 员工新增/编辑弹窗组件
- 支持表单验证和数据提交
- 部门和职位选择下拉框
- 完整的员工信息表单，包括个人信息和管理信息

### 3. API调用
- `employeeManagement.js` - 前端API调用封装

## 权限配置

系统使用以下权限控制：
- `system_employee_select` - 查询权限
- `system_employee_insert` - 新增权限
- `system_employee_update` - 修改权限
- `system_employee_delete` - 删除权限

## 使用说明

### 1. 访问路径
前端页面路径：`/views/modules/system/employeeManagement/`

### 2. API端点
- GET `/api/v1/system/employee/findPage` - 分页查询
- GET `/api/v1/system/employee/listAll` - 查询所有数据
- POST `/api/v1/system/employee/insert` - 新增员工
- POST `/api/v1/system/employee/update` - 修改员工
- POST `/api/v1/system/employee/del` - 删除员工
- GET `/api/v1/system/employee/findByDepartmentId` - 按部门查询员工
- GET `/api/v1/system/employee/findByPositionId` - 按职位查询员工
- POST `/api/v1/system/employee/checkEmployeeNumberUnique` - 检查员工编号唯一性
- POST `/api/v1/system/employee/checkIdCardNumberUnique` - 检查身份证号唯一性

### 3. 主要特性
- **多租户支持**：数据按租户隔离
- **月份管理**：支持按月份查看和管理员工数据
- **编号唯一性**：同一租户同一月份下员工编号唯一
- **身份证唯一性**：同一租户同一月份下身份证号唯一
- **部门职位关联**：员工与部门、职位的关联关系
- **状态管理**：支持在职、离职、试用期状态
- **管理信息**：社保缴纳、合同签订、转正状态、资料签订等管理信息

### 4. 注意事项
- 员工编号和身份证号在同一租户同一月份下必须唯一
- 删除操作为逻辑删除，不会物理删除数据
- 新增员工时会自动设置当前用户的租户ID
- 月份选择器与全局状态同步
- 员工与部门、职位存在关联关系，可按部门或职位筛选员工

## 文件清单

### 后端文件
```
opsli-boot/opsli-modulars/opsli-modulars-system/src/main/java/org/opsli/modulars/system/employee/
├── entity/EmployeeMonthly.java
├── mapper/EmployeeMapper.java
├── mapper/xml/EmployeeMapper.xml
├── service/IEmployeeService.java
├── service/impl/EmployeeServiceImpl.java
└── web/EmployeeRestController.java

opsli-boot/opsli-api/src/main/java/org/opsli/api/
├── web/system/employee/EmployeeApi.java
└── wrapper/system/employee/EmployeeModel.java
```

### 前端文件
```
opsli-ui/vue-admin-better/src/
├── views/modules/system/employeeManagement/
│   ├── index.vue
│   └── components/EmployeeManagementEdit.vue
└── api/system/employee/employeeManagement.js
```

## 架构特点

该实现严格遵循OPSLI框架的标准模式和Position Management的架构设计：

1. **分层架构**：API层、Web层、Service层、Mapper层清晰分离
2. **统一规范**：命名规范、代码结构与Position Management保持一致
3. **权限控制**：集成OPSLI权限体系
4. **多租户支持**：完整的多租户数据隔离
5. **月份管理**：支持按月份进行数据管理
6. **前后端分离**：Vue.js前端与Spring Boot后端分离
7. **数据验证**：前后端双重数据验证
8. **错误处理**：统一的错误处理机制

## 扩展说明

该实现可以作为其他模块开发的参考模板。如需扩展功能，可以：

1. 添加更多的员工属性字段
2. 实现员工层级关系
3. 添加员工考勤管理
4. 实现员工薪资管理
5. 添加员工统计报表

所有扩展都应遵循现有的架构模式和代码规范。
