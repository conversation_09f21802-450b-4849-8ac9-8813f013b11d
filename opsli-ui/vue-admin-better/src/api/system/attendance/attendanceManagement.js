import request from "@/utils/request";

/**
 * 获取考勤分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/attendance/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有考勤数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/attendance/listAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个考勤数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/attendance/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增考勤记录
 * @param {Object} data - 考勤数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/attendance/insert",
    method: "post",
    data,
  });
}

/**
 * 更新考勤记录
 * @param {Object} data - 考勤数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/attendance/update",
    method: "post",
    data,
  });
}

/**
 * 删除考勤记录
 * @param {Object} data - 考勤ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/attendance/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除考勤记录
 * @param {Object} data - 考勤ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/attendance/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 根据员工ID获取考勤列表
 * @param {Object} data - 查询参数 {tenantId, dataMonth, employeeId}
 * @returns {Promise} - 返回请求Promise
 */
export function findByEmployeeId(data) {
  return request({
    url: "/api/v1/system/attendance/findByEmployeeId",
    method: "get",
    params: data,
  });
}

/**
 * 根据部门ID获取考勤列表
 * @param {Object} data - 查询参数 {tenantId, dataMonth, departmentId}
 * @returns {Promise} - 返回请求Promise
 */
export function findByDepartmentId(data) {
  return request({
    url: "/api/v1/system/attendance/findByDepartmentId",
    method: "get",
    params: data,
  });
}

/**
 * 根据考勤状态获取考勤列表
 * @param {Object} data - 查询参数 {tenantId, dataMonth, status}
 * @returns {Promise} - 返回请求Promise
 */
export function findByStatus(data) {
  return request({
    url: "/api/v1/system/attendance/findByStatus",
    method: "get",
    params: data,
  });
}

/**
 * 获取考勤请求参数配置
 * @param {Object} data - 查询参数
 * @returns {Object} - 返回请求配置
 */
export function getListParams(data) {
  return {
    url: "/api/v1/system/attendance/findPage",
    method: "get",
    params: data,
  };
}
