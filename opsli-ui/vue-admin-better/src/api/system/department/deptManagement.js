import request from "@/utils/request";

/**
 * 获取部门分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/department/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有部门数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/department/listAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个部门数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/department/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增部门
 * @param {Object} data - 部门数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/department/insert",
    method: "post",
    data,
  });
}

/**
 * 更新部门
 * @param {Object} data - 部门数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/department/update",
    method: "post",
    data,
  });
}

/**
 * 删除部门
 * @param {Object} data - 部门ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/department/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除部门
 * @param {Object} data - 部门ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/department/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 根据父部门ID获取子部门列表
 * @param {Object} data - 查询参数 {tenantId, dataMonth, parentId}
 * @returns {Promise} - 返回请求Promise
 */
export function findByParentId(data) {
  return request({
    url: "/api/v1/system/department/findByParentId",
    method: "get",
    params: data,
  });
}

/**
 * 检查部门名称是否唯一
 * @param {Object} data - 部门数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkNameUnique(data) {
  return request({
    url: "/api/v1/system/department/checkNameUnique",
    method: "post",
    data,
  });
}

/**
 * 检查部门编码是否唯一
 * @param {Object} data - 部门数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkCodeUnique(data) {
  return request({
    url: "/api/v1/system/department/checkCodeUnique",
    method: "post",
    data,
  });
}

/**
 * 获取部门请求参数配置
 * @param {Object} data - 查询参数
 * @returns {Object} - 返回请求配置
 */
export function getListParams(data) {
  return {
    url: "/api/v1/system/department/findPage",
    method: "get",
    params: data,
  };
}