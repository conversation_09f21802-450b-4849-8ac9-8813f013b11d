import request from "@/utils/request";

/**
 * 获取职位分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/position/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有职位数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/position/listAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个职位数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/position/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增职位
 * @param {Object} data - 职位数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/position/insert",
    method: "post",
    data,
  });
}

/**
 * 更新职位
 * @param {Object} data - 职位数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/position/update",
    method: "post",
    data,
  });
}

/**
 * 删除职位
 * @param {Object} data - 职位ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/position/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除职位
 * @param {Object} data - 职位ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/position/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 根据部门ID获取职位列表
 * @param {Object} data - 查询参数 {tenantId, dataMonth, departmentId}
 * @returns {Promise} - 返回请求Promise
 */
export function findByDepartmentId(data) {
  return request({
    url: "/api/v1/system/position/findByDepartmentId",
    method: "get",
    params: data,
  });
}

/**
 * 检查职位编码是否唯一
 * @param {Object} data - 职位数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkCodeUnique(data) {
  return request({
    url: "/api/v1/system/position/checkCodeUnique",
    method: "post",
    data,
  });
}

/**
 * 检查职位名称是否唯一
 * @param {Object} data - 职位数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkTitleUnique(data) {
  return request({
    url: "/api/v1/system/position/checkTitleUnique",
    method: "post",
    data,
  });
}

/**
 * 获取职位请求参数配置
 * @param {Object} data - 查询参数
 * @returns {Object} - 返回请求配置
 */
export function getListParams(data) {
  return {
    url: "/api/v1/system/position/findPage",
    method: "get",
    params: data,
  };
}
