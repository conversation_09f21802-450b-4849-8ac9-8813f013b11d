import request from "@/utils/request";

/**
 * 获取员工分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/employee/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有员工数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/employee/listAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个员工数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/employee/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增员工
 * @param {Object} data - 员工数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/employee/insert",
    method: "post",
    data,
  });
}

/**
 * 更新员工
 * @param {Object} data - 员工数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/employee/update",
    method: "post",
    data,
  });
}

/**
 * 删除员工
 * @param {Object} data - 员工ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/employee/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除员工
 * @param {Object} data - 员工ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/employee/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 根据部门ID获取员工列表
 * @param {Object} data - 查询参数 {tenantId, dataMonth, departmentId}
 * @returns {Promise} - 返回请求Promise
 */
export function findByDepartmentId(data) {
  return request({
    url: "/api/v1/system/employee/findByDepartmentId",
    method: "get",
    params: data,
  });
}

/**
 * 根据职位ID获取员工列表
 * @param {Object} data - 查询参数 {tenantId, dataMonth, positionId}
 * @returns {Promise} - 返回请求Promise
 */
export function findByPositionId(data) {
  return request({
    url: "/api/v1/system/employee/findByPositionId",
    method: "get",
    params: data,
  });
}

/**
 * 检查员工编号是否唯一
 * @param {Object} data - 员工数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkEmployeeNumberUnique(data) {
  return request({
    url: "/api/v1/system/employee/checkEmployeeNumberUnique",
    method: "post",
    data,
  });
}

/**
 * 检查身份证号是否唯一
 * @param {Object} data - 员工数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkIdCardNumberUnique(data) {
  return request({
    url: "/api/v1/system/employee/checkIdCardNumberUnique",
    method: "post",
    data,
  });
}

/**
 * 获取员工请求参数配置
 * @param {Object} data - 查询参数
 * @returns {Object} - 返回请求配置
 */
export function getListParams(data) {
  return {
    url: "/api/v1/system/employee/findPage",
    method: "get",
    params: data,
  };
}
