<template>
  <el-date-picker
    v-model="selectedMonth"
    type="month"
    placeholder="选择月份"
    format="yyyy-MM"
    value-format="yyyy-MM"
    :clearable="false"
  ></el-date-picker>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'MonthPicker',
  computed: {
    ...mapGetters({
      monthFromStore: 'month/selectedMonth',
    }),
    selectedMonth: {
      get() {
        return this.monthFromStore
      },
      set(val) {
        this.setSelectedMonth(val)
      },
    },
  },
  methods: {
    ...mapActions({
      setSelectedMonth: 'month/setSelectedMonth',
    }),
  },
}
</script>
