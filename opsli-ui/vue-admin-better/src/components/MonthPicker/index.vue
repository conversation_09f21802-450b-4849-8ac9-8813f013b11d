<template>
  <div class="month-picker-container">
    <el-date-picker
      v-model="selectedDate"
      type="month"
      placeholder="选择月份"
      format="yyyy-MM"
      value-format="yyyy-MM"
      size="small"
      :clearable="false"
      style="width: 120px"
      @change="handleMonthChange"
    />
  </div>
</template>

<script>
  import { mapGetters } from "vuex";

  export default {
    name: "MonthPicker",
    data() {
      return {
        selectedDate: null,
      };
    },
    computed: {
      ...mapGetters({
        currentMonth: "user/currentMonth",
      }),
    },
    watch: {
      currentMonth: {
        handler(newVal) {
          this.selectedDate = newVal;
        },
        immediate: true,
      },
    },
    methods: {
      handleMonthChange(value) {
        if (value) {
          // 更新全局状态
          this.$store.dispatch("user/updateCurrentMonth", value);

          // 触发自定义事件，通知父组件月份已改变
          this.$emit("month-change", value);

          // 发送全局事件，其他组件可以监听这个事件来更新数据
          this.$baseEventBus.$emit("month-changed", value);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .month-picker-container {
    display: inline-block;

    ::v-deep .el-input__inner {
      color: inherit;
      background-color: transparent;
      border: 1px solid #dcdfe6;

      &:hover {
        border-color: #c0c4cc;
      }

      &:focus {
        border-color: #409eff;
      }
    }

    ::v-deep .el-input__suffix {
      color: inherit;
    }

    ::v-deep .el-input__prefix {
      color: inherit;
    }
  }
</style>
