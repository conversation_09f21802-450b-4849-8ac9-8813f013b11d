<template>
  <div class="nav-bar-container">
    <el-row :gutter="15">
      <el-col :xs="4" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="left-panel">
          <i
            :class="collapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
            :title="collapse ? '展开' : '收起'"
            class="fold-unfold"
            @click="handleCollapse"
          ></i>
          <vab-breadcrumb class="hidden-xs-only" />
        </div>
      </el-col>
      <el-col :xs="20" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="right-panel">
          <!-- 租户信息 -->
          <div
            v-if="tenantName && tenantName.trim()"
            class="tenant-info hidden-sm-and-down"
          >
            <vab-icon :icon="['fas', 'building']" />
            <span class="tenant-name">{{ tenantName }}</span>
          </div>

          <!-- 账户有效期 -->
          <div v-if="expireTime" class="expire-info hidden-md-and-down">
            <vab-icon :icon="['fas', 'clock']" />
            <span class="expire-text" :class="expireClass">
              {{ expireText }}
            </span>
          </div>

          <!-- 月份选择器 -->
          <div class="month-picker-wrapper">
            <month-picker />
          </div>
          <vab-error-log />
          <vab-full-screen-bar @refresh="refreshRoute" />
          <vab-theme-bar class="hidden-xs-only" />
          <vab-icon
            title="重载所有路由"
            :pulse="pulse"
            :icon="['fas', 'redo']"
            @click="refreshRoute"
          />
          <vab-avatar />
          <!--  <vab-icon
            title="退出系统"
            :icon="['fas', 'sign-out-alt']"
            @click="logout"
          />-->
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import { mapActions, mapGetters } from "vuex";
  import MonthPicker from "@/components/MonthPicker";
  import dayjs from "dayjs";

  export default {
    name: "VabNavBar",
    components: { MonthPicker },
    data() {
      return {
        pulse: false,
      };
    },
    computed: {
      ...mapGetters({
        collapse: "settings/collapse",
        visitedRoutes: "tabsBar/visitedRoutes",
        device: "settings/device",
        routes: "routes/routes",
        avatar: "user/avatar",
        tenantName: "user/tenantName",
        expireTime: "user/expireTime",
      }),
      expireText() {
        if (!this.expireTime) {
          return "";
        }
        const now = dayjs();
        const expire = dayjs(this.expireTime);
        const diff = expire.diff(now, "day");

        if (diff < 0) {
          return "已过期";
        }
        if (diff <= 30) {
          return `${diff}天后过期`;
        }
        return `有效期至 ${expire.format("YYYY-MM-DD")}`;
      },
      expireClass() {
        if (!this.expireTime) {
          return "";
        }
        const now = dayjs();
        const expire = dayjs(this.expireTime);
        const diff = expire.diff(now, "day");

        if (diff < 0) {
          return "is-expired";
        }
        if (diff <= 7) {
          return "is-warning";
        }
        return "";
      },
    },
    methods: {
      ...mapActions({
        changeCollapse: "settings/changeCollapse",
      }),
      handleCollapse() {
        this.changeCollapse();
      },
      async refreshRoute() {
        this.$baseEventBus.$emit("reload-router-view");
        this.pulse = true;
        setTimeout(() => {
          this.pulse = false;
        }, 1000);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .nav-bar-container {
    position: relative;
    height: $base-nav-bar-height;
    padding-right: $base-padding;
    padding-left: $base-padding;
    overflow: hidden;
    user-select: none;
    background: $base-color-white;
    box-shadow: $base-box-shadow;

    .left-panel {
      display: flex;
      align-items: center;
      justify-items: center;
      height: $base-nav-bar-height;

      .fold-unfold {
        color: $base-color-gray;
        cursor: pointer;
      }

      ::v-deep {
        .breadcrumb-container {
          margin-left: 10px;
        }
      }
    }

    .right-panel {
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: flex-end;
      height: $base-nav-bar-height;

      .tenant-info,
      .expire-info,
      .month-picker-wrapper {
        display: flex;
        align-items: center;
        height: $base-nav-bar-height;
        margin-right: 15px;
        font-size: $base-font-size-default;
        color: $base-color-black;

        .vab-icon {
          margin-right: 5px;
        }

        .expire-text {
          &.is-expired {
            color: #f56c6c; // Element UI Danger color
          }
          &.is-warning {
            color: #e6a23c; // Element UI Warning color
          }
        }
      }

      ::v-deep {
        svg {
          width: 1em;
          height: 1em;
          margin-right: 15px;
          font-size: $base-font-size-small;
          color: $base-color-gray;
          cursor: pointer;
          fill: $base-color-gray;
        }

        button {
          svg {
            margin-right: 0;
            color: $base-color-white;
            cursor: pointer;
            fill: $base-color-white;
          }
        }

        .el-badge {
          margin-right: 15px;
        }
      }
    }
  }
</style>
