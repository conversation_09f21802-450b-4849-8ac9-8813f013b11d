<template>
  <div class="test-container">
    <el-card>
      <div slot="header">
        <span>TopBar功能测试</span>
      </div>
      
      <div class="test-section">
        <h3>当前用户信息</h3>
        <p><strong>用户名:</strong> {{ username }}</p>
        <p><strong>租户ID:</strong> {{ tenantId }}</p>
        <p><strong>租户名称:</strong> {{ tenantName }}</p>
        <p><strong>账户有效期:</strong> {{ expireTime }}</p>
        <p><strong>当前选择月份:</strong> {{ currentMonth }}</p>
      </div>
      
      <div class="test-section">
        <h3>月份选择器测试</h3>
        <month-picker @month-change="onMonthChange" />
        <p>选择的月份: {{ selectedMonth }}</p>
      </div>
      
      <div class="test-section">
        <h3>模拟数据测试</h3>
        <el-button @click="simulateTenantData">模拟租户数据</el-button>
        <el-button @click="simulateExpireData">模拟有效期数据</el-button>
        <el-button @click="clearData">清除数据</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import MonthPicker from '@/components/MonthPicker'

export default {
  name: 'TopBarTest',
  components: {
    MonthPicker
  },
  data() {
    return {
      selectedMonth: ''
    }
  },
  computed: {
    ...mapGetters({
      username: 'user/username',
      tenantId: 'user/tenantId',
      tenantName: 'user/tenantName',
      expireTime: 'user/expireTime',
      currentMonth: 'user/currentMonth'
    })
  },
  methods: {
    onMonthChange(month) {
      this.selectedMonth = month
      this.$message.success(`月份已切换到: ${month}`)
    },
    simulateTenantData() {
      this.$store.commit('user/setTenantId', 'tenant_001')
      this.$store.commit('user/setTenantName', '测试租户公司')
      this.$message.success('已设置模拟租户数据')
    },
    simulateExpireData() {
      // 设置7天后过期
      const expireDate = new Date()
      expireDate.setDate(expireDate.getDate() + 7)
      this.$store.commit('user/setExpireTime', expireDate)
      this.$message.success('已设置模拟有效期数据（7天后过期）')
    },
    clearData() {
      this.$store.commit('user/setTenantId', '')
      this.$store.commit('user/setTenantName', '')
      this.$store.commit('user/setExpireTime', null)
      this.$message.success('已清除模拟数据')
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  
  h3 {
    color: #409eff;
    margin-bottom: 15px;
  }
  
  p {
    margin: 8px 0;
  }
  
  .el-button {
    margin-right: 10px;
  }
}
</style>
