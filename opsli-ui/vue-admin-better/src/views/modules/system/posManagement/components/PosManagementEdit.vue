<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    width="800px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="125px">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="职位名称" prop="title">
            <el-input v-model="form.title" autocomplete="off"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="职位编码" prop="positionCode">
            <el-input v-model="form.positionCode" autocomplete="off"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="所属部门" prop="departmentId">
            <el-select 
              v-model="form.departmentId" 
              placeholder="请选择所属部门" 
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in deptList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="职位级别" prop="positionLevel">
            <el-select v-model="form.positionLevel" placeholder="请选择职位级别">
              <el-option
                v-for="item in posLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="编制人数" prop="headcount">
            <el-input-number 
              v-model="form.headcount" 
              :min="0" 
              :max="9999"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="form.status"
              :active-value="1"
              :inactive-value="0"
            ></el-switch>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="职位描述" prop="description">
            <el-input type="textarea" v-model="form.description" autocomplete="off" :rows="3"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="职位要求" prop="requirements">
            <el-input type="textarea" v-model="form.requirements" autocomplete="off" :rows="3"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { isNull } from "@/utils/validate";
import { doInsert, doUpdate } from "@/api/system/position/posManagement";

export default {
  name: "PosManagementEdit",
  props: {
    deptList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 职位级别 字典
      posLevelOptions: [
        { label: '初级', value: '1' },
        { label: '中级', value: '2' },
        { label: '高级', value: '3' }
      ],
      form: {
        tenantId: null,
        dataMonth: null,
        title: '',
        positionCode: '',
        description: '',
        requirements: '',
        departmentId: null,
        positionLevel: '',
        headcount: 1,
        status: 1,
        version: 0
      },
      originalTitle: '',
      originalCode: '',
      dict: {},
      rules: {
        title: [
          { required: true, trigger: "blur", message: "请输入职位名称" },
        ],
        positionCode: [
          { required: true, trigger: "blur", message: "请输入职位编码" },
        ],
        status: [
          { required: true, trigger: "change", message: "请选择状态" }
        ],
        description: [
          { max: 1000, message: '职位描述不能超过1000个字符', trigger: 'blur' }
        ],
        requirements: [
          { max: 1000, message: '职位要求不能超过1000个字符', trigger: 'blur' }
        ],
        positionLevel: [
          { max: 50, message: '职位级别不能超过50个字符', trigger: 'blur' }
        ],
        headcount: [
          { required: true, trigger: "blur", message: "请输入编制人数" },
          { type: 'number', min: 0, message: '编制人数不能小于0', trigger: 'blur' }
        ]
      },
      title: "",
      dialogFormVisible: false,
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
      selectedMonth: 'month/selectedMonth',
    }),
  },
  mounted() {
    // 获取字典数据
    this.dict.common_status = this.$getDictList("common_status");
  },
  methods: {
    // 显示编辑对话框
    async showEdit(row, dataMonth) {
      // 设置表单默认值
      this.form.tenantId = this.tenantId;
      this.form.dataMonth = dataMonth || this.selectedMonth;
      
      if (!row) {
        // 新增模式
        this.title = "添加职位";
      } else {
        // 编辑模式
        this.title = "编辑职位";
        this.form = Object.assign({}, row);
        this.originalTitle = row.title;
        this.originalCode = row.positionCode;
      }
      
      this.dialogFormVisible = true;
    },
    
    // 关闭对话框
    close() {
      this.dialogFormVisible = false;
      this.$refs["form"].resetFields();
      this.form = this.$options.data().form;
      this.originalTitle = '';
      this.originalCode = '';
    },
    
    // 保存职位
    save() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          try {
            // 修改
            if (!isNull(this.form.id)) {
              const { msg } = await doUpdate(this.form);
              this.$baseMessage(msg, "success");
            } else {
              const { msg } = await doInsert(this.form);
              this.$baseMessage(msg, "success");
            }
            
            await this.$emit("fetchData");
            this.close();
          } catch (error) {
            console.error("保存职位失败:", error);
            this.$baseMessage("保存职位失败，请稍后重试", "error");
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>
