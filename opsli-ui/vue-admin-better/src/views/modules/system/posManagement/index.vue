<template>
  <div class="posManagement-container">
    <el-row :gutter="15">
      <!-- 职位管理主表 -->
      <el-col>
        <vab-query-form>
          <vab-query-form-left-panel :span="6">
            <el-button
              v-if="$perms('system_position_insert')"
              icon="el-icon-plus"
              type="primary"
              @click="handleInsert"
            > 添加 </el-button>
          </vab-query-form-left-panel>
          <vab-query-form-right-panel :span="18">
            <el-form :inline="true" :model="queryForm" @submit.native.prevent>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm.title_LIKE"
                  placeholder="请输入职位名称"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm.positionCode_LIKE"
                  placeholder="请输入职位编码"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryForm.departmentId_EQ"
                  placeholder="请选择部门"
                  clearable
                  style="width: 150px"
                  filterable
                >
                  <el-option
                    v-for="item in deptList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" type="primary" @click="queryData">
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-right-panel>
        </vab-query-form>

        <el-table
          ref="posTable"
          v-loading="listLoading"
          :data="tableData"
          :element-loading-text="elementLoadingText"
          :highlight-current-row="true"
          @current-change="setSelectRows"
        >
          <el-table-column
            show-overflow-tooltip
            prop="title"
            label="职位名称"
            min-width="120"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="positionCode"
            label="职位编码"
            min-width="120"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="departmentId"
            label="所属部门"
            min-width="120"
            :formatter="formatDepartment"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="positionLevel"
            label="职位级别"
            min-width="100">
            <template slot-scope="scope">
              <span>{{ posLevelDict[scope.row.positionLevel] || '' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="headcount"
            label="编制人数"
            min-width="100"
            align="center"
          ></el-table-column>

          <el-table-column show-overflow-tooltip label="状态" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(row)"
              ></el-switch>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="description"
            label="职位描述"
            min-width="150"
          ></el-table-column>

          <el-table-column
            fixed="right"
            show-overflow-tooltip
            label="操作"
            width="160"
            v-if="$perms('system_position_update') || $perms('system_position_delete')"
          >
            <template v-slot="scope">
              <el-button
                v-if="$perms('system_position_update')"
                type="text"
                @click="handleUpdate(scope.row)"
              > 编辑 </el-button>

              <el-divider direction="vertical"></el-divider>

              <el-button
                v-if="$perms('system_position_delete')"
                type="text"
                @click="handleDelete(scope.row)"
              > 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          :current-page="queryForm.pageNo"
          :page-size="queryForm.pageSize"
          :layout="layout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-col>
    </el-row>

    <!-- 编辑 -->
    <pos-edit ref="edit" :dept-list="deptList" @fetchData="fetchData"></pos-edit>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getList, doDelete, doUpdate } from '@/api/system/position/posManagement';
import { getAll as getAllDepts } from '@/api/system/department/deptManagement';
import PosEdit from './components/PosManagementEdit'

export default {
  name: "PosManagement",
  components: { PosEdit },
  data () {
    return {
      // 职位级别 字典
      posLevelDict: {
        1: '初级',
        2: '中级',
        3: '高级'
      },
      // 当前行
      currentRow: null,
      // 字典
      dict: {},
      // 查询表单
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        title_LIKE: "",
        positionCode_LIKE: "",
        departmentId_EQ: "",
        status_EQ: "",
        tenantId: null,
        dataMonth: null
      },
      // 返回数据
      tableData: [],
      // 部门列表
      deptList: [],
      // list 加载状态
      listLoading: true,
      // 总条数
      total: 0,
      elementLoadingText: "正在加载...",
      layout: "total, sizes, prev, pager, next, jumper",
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
      selectedMonth: 'month/selectedMonth',
    }),
    readyToFetch() {
      return this.tenantId && this.selectedMonth;
    },
  },
  watch: {
    readyToFetch: {
      handler(isReady) {
        if (isReady) {
          this.fetchData();
          this.fetchAllDepts();
        }
      },
      immediate: true,
    },
  },
  created() {
    this.dict.common_status = this.$getDictList("common_status");
  },
  methods: {
    setSelectRows(val) {
      this.currentRow = val;
    },
    handleInsert() {
      this.$refs["edit"].showEdit(null, this.queryForm.dataMonth);
    },
    handleUpdate(row) {
      if (row.id) {
        this.$refs["edit"].showEdit(row);
      }
    },
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前职位吗", null, async () => {
          const { msg } = await doDelete({ id: row.id });
          this.$baseMessage(msg, "success");
          await this.fetchData();
        });
      }
    },
    handleSizeChange(val) {
      this.queryForm.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val;
      this.fetchData();
    },
    queryData() {
      this.queryForm.pageNo = 1;
      this.fetchData();
    },
    async fetchAllDepts() {
      if (!this.tenantId || !this.selectedMonth) {
        return;
      }
      const { data } = await getAllDepts({
        tenantId: this.tenantId,
        dataMonth: this.selectedMonth,
      });
      this.deptList = data;
    },

    // 格式化部门
    formatDepartment(row, column, cellValue) {
      if (cellValue) {
        const dept = this.deptList.find((dept) => dept.id === cellValue);
        return dept ? dept.name : '';
      }
      return '无';
    },

    async fetchData() {
      if (!this.tenantId || !this.selectedMonth) {
        return;
      }
      this.queryForm.tenantId = this.tenantId;
      this.queryForm.dataMonth = this.selectedMonth;
      this.listLoading = true;
      const { data } = await getList(this.queryForm);
      console.log("data", data);
      this.tableData = data.rows;
      console.log("tableData", this.tableData);
      this.total = Number(data.total);
      console.log("total", this.total);
      this.listLoading = false;
    },

    // 状态切换
    async handleStatusChange(row) {
      try {
        const newStatus = row.status;
        await this.$baseConfirm(
          `你确定要${
            newStatus === 1 ? '启用' : '禁用'
          } "${row.title}" 职位吗？`,
          null,
          async () => {
            const { msg } = await doUpdate({ ...row, status: newStatus });
            this.$baseMessage(msg, 'success');
            // 无需手动刷新，v-model已更新视图
          },
          async () => {
            // 如果用户取消，则将开关状态恢复原状
            row.status = newStatus === 1 ? 0 : 1;
          }
        );
      } catch (error) {
        console.error('状态更新失败:', error);
        this.$baseMessage('状态更新失败', 'error');
        // 发生错误时也恢复状态
        row.status = row.status === 1 ? 0 : 1;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.posManagement-container {
  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
  }
}
</style>