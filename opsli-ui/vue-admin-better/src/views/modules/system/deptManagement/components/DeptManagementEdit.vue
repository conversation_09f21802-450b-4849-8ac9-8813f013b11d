<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    width="800px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="125px">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="部门名称" prop="name">
            <el-input v-model="form.name" autocomplete="off"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="部门编码" prop="deptCode">
            <el-input v-model="form.deptCode" autocomplete="off"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="上级部门" prop="parentDepartmentId">
            <el-select 
              v-model="form.parentDepartmentId" 
              placeholder="请选择上级部门" 
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in availableDepts"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="form.status"
              :active-value="1"
              :inactive-value="0"
            ></el-switch>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="负责人ID" prop="responsiblePerson">
            <el-input v-model="form.responsiblePerson" autocomplete="off"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="职责描述" prop="responsibilities">
            <el-input type="textarea" v-model="form.responsibilities" autocomplete="off" :rows="3"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { isNull } from "@/utils/validate";
import { doInsert, doUpdate } from "@/api/system/department/deptManagement";

export default {
  name: "DeptManagementEdit",
  props: {
    deptList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        tenantId: null,
        dataMonth: null,
        name: '',
        deptCode: '',
        parentDepartmentId: null,
        status: 1,
        responsiblePerson: null,
        responsibilities: '',
        version: 0
      },
      originalName: '',
      originalCode: '',
      dict: {},
      rules: {
        name: [
          { required: true, trigger: "blur", message: "请输入部门名称" },
        ],
        deptCode: [
          { required: true, trigger: "blur", message: "请输入部门编码" },
        ],
        status: [
          { required: true, trigger: "change", message: "请选择状态" }
        ],
        responsibilities: [
          { max: 500, message: '职责描述不能超过500个字符', trigger: 'blur' }
        ]
      },
      title: "",
      dialogFormVisible: false,
      parentDepartment: null
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
      selectedMonth: 'month/selectedMonth',
    }),
    availableDepts() {
      if (!this.form.id) {
        // 新增模式，返回所有部门
        return this.deptList;
      }
      // 编辑模式，过滤掉自身和子部门
      const childrenIds = this.getChildrenIds(this.deptList, this.form.id);
      const selfAndChildrenIds = [this.form.id, ...childrenIds];
      return this.deptList.filter(dept => !selfAndChildrenIds.includes(dept.id));
    },
  },
  mounted() {
    // 获取字典数据
    this.dict.common_status = this.$getDictList("common_status");
  },
  methods: {
    // 递归获取所有子部门的ID
    getChildrenIds(deptList, parentId) {
      const children = deptList.filter(dept => dept.parentDepartmentId === parentId);
      let ids = children.map(child => child.id);
      children.forEach(child => {
        ids = ids.concat(this.getChildrenIds(deptList, child.id));
      });
      return ids;
    },
    
    // 显示编辑对话框
    async showEdit(row, dataMonth, parentDept) {
      // 设置表单默认值
      this.form.tenantId = this.tenantId;
      this.form.dataMonth = dataMonth || this.selectedMonth;
      
      if (!row) {
        // 新增模式
        this.title = "添加部门";
        
        // 如果有父部门参数，设置父部门ID
        if (parentDept) {
          this.form.parentDepartmentId = parentDept.id;
          this.parentDepartment = parentDept;
        }
      } else {
        // 编辑模式
        this.title = "编辑部门";
        this.form = Object.assign({}, row);
        this.originalName = row.name;
        this.originalCode = row.deptCode;
      }
      
      this.dialogFormVisible = true;
    },
    
    // 关闭对话框
    close() {
      this.dialogFormVisible = false;
      this.$refs["form"].resetFields();
      this.form = this.$options.data().form;
      this.originalName = '';
      this.originalCode = '';
      this.parentDepartment = null;
    },
    
    // 保存部门
    save() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          try {
            // 修改
            if (!isNull(this.form.id)) {
              const { msg } = await doUpdate(this.form);
              this.$baseMessage(msg, "success");
            } else {
              const { msg } = await doInsert(this.form);
              this.$baseMessage(msg, "success");
            }
            
            await this.$emit("fetchData");
            this.close();
          } catch (error) {
            console.error("保存部门失败:", error);
            this.$baseMessage("保存部门失败，请稍后重试", "error");
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>