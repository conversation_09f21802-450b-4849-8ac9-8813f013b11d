<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    width="1000px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="125px">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="员工编号" prop="employeeNumber">
            <el-input v-model="form.employeeNumber" autocomplete="off"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="姓名" prop="fullName">
            <el-input v-model="form.fullName" autocomplete="off"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="form.gender">
              <el-radio label="男">男</el-radio>
              <el-radio label="女">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="出生日期" prop="dateOfBirth">
            <el-date-picker
              v-model="form.dateOfBirth"
              type="date"
              placeholder="选择出生日期"
              style="width: 100%"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="手机号码" prop="phoneNumber">
            <el-input v-model="form.phoneNumber" autocomplete="off"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="身份证号" prop="idCardNumber">
            <el-input v-model="form.idCardNumber" autocomplete="off" placeholder="请输入身份证号"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="所属部门" prop="departmentId">
            <el-select 
              v-model="form.departmentId" 
              placeholder="请选择所属部门" 
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in deptList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="主职位" prop="positionId">
            <el-select 
              v-model="form.positionId" 
              placeholder="请选择主职位" 
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in positionList"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="兼岗位" prop="concurrentPositionId">
            <el-select 
              v-model="form.concurrentPositionId" 
              placeholder="请选择兼岗位" 
              style="width: 100%"
              filterable
              clearable
            >
              <el-option label="无" :value="null"></el-option>
              <el-option
                v-for="item in positionList"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="入职日期" prop="hireDate">
            <el-date-picker
              v-model="form.hireDate"
              type="date"
              placeholder="选择入职日期"
              style="width: 100%"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="员工状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="在职">在职</el-radio>
              <el-radio label="离职">离职</el-radio>
              <el-radio label="试用期">试用期</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="管理信息">
            <el-row :gutter="10">
              <el-col :span="6">
                <el-checkbox v-model="form.hasSocialInsurance">社保缴纳</el-checkbox>
              </el-col>
              <el-col :span="6">
                <el-checkbox v-model="form.isContractSigned">签订合同</el-checkbox>
              </el-col>
              <el-col :span="6">
                <el-checkbox v-model="form.isProbationPassed">已转正</el-checkbox>
              </el-col>
              <el-col :span="6">
                <el-checkbox v-model="form.areDocumentsSigned">签订资料</el-checkbox>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="个人简介" prop="personalBio">
            <el-input type="textarea" v-model="form.personalBio" autocomplete="off" :rows="4" placeholder="请输入个人简介"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { isNull } from "@/utils/validate";
import { doInsert, doUpdate } from "@/api/system/employee/employeeManagement";

export default {
  name: "EmployeeManagementEdit",
  props: {
    deptList: {
      type: Array,
      default: () => [],
    },
    positionList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        tenantId: null,
        dataMonth: null,
        employeeNumber: '',
        fullName: '',
        gender: '',
        dateOfBirth: null,
        phoneNumber: '',
        idCardNumber: '',
        departmentId: null,
        positionId: null,
        concurrentPositionId: null,
        hireDate: null,
        status: '试用期',
        hasSocialInsurance: false,
        isContractSigned: false,
        isProbationPassed: false,
        areDocumentsSigned: false,
        personalBio: '',
        version: 0
      },
      originalEmployeeNumber: '',
      originalIdCardNumber: '',
      dict: {},
      rules: {
        employeeNumber: [
          { required: true, trigger: "blur", message: "请输入员工编号" },
          { max: 50, message: '员工编号不能超过50个字符', trigger: 'blur' }
        ],
        fullName: [
          { required: true, trigger: "blur", message: "请输入员工姓名" },
          { max: 255, message: '员工姓名不能超过255个字符', trigger: 'blur' }
        ],
        phoneNumber: [
          { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
        ],
        idCardNumber: [
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '身份证号格式不正确', trigger: 'blur' }
        ],
        departmentId: [
          { required: true, trigger: "change", message: "请选择所属部门" }
        ],
        positionId: [
          { required: true, trigger: "change", message: "请选择主职位" }
        ],
        personalBio: [
          { max: 2000, message: '个人简介不能超过2000个字符', trigger: 'blur' }
        ]
      },
      title: "",
      dialogFormVisible: false,
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
      selectedMonth: 'month/selectedMonth',
    }),
  },
  mounted() {
    // 获取字典数据
    this.dict.employee_status = this.$getDictList("employee_status");
    this.dict.gender = this.$getDictList("gender");
  },
  methods: {
    // 显示编辑对话框
    async showEdit(row, dataMonth) {
      // 设置表单默认值
      this.form.tenantId = this.tenantId;
      this.form.dataMonth = dataMonth || this.selectedMonth;

      if (!row) {
        // 新增模式
        this.title = "添加员工";
      } else {
        // 编辑模式
        this.title = "编辑员工";
        this.form = Object.assign({}, row);
        this.originalEmployeeNumber = row.employeeNumber;
        this.originalIdCardNumber = row.idCardNumber;
      }

      this.dialogFormVisible = true;
    },

    // 关闭对话框
    close() {
      this.dialogFormVisible = false;
      this.$refs["form"].resetFields();
      this.form = this.$options.data().form;
      this.originalEmployeeNumber = '';
      this.originalIdCardNumber = '';
    },

    // 保存员工
    save() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          try {
            // 修改
            if (!isNull(this.form.id)) {
              const { msg } = await doUpdate(this.form);
              this.$baseMessage(msg, "success");
            } else {
              const { msg } = await doInsert(this.form);
              this.$baseMessage(msg, "success");
            }

            await this.$emit("fetchData");
            this.close();
          } catch (error) {
            console.error("保存员工失败:", error);
            this.$baseMessage("保存员工失败，请稍后重试", "error");
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>
