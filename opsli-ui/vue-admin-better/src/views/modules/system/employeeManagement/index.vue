<template>
  <div class="employeeManagement-container">
    <el-row :gutter="15">
      <!-- 员工管理主表 -->
      <el-col>
        <vab-query-form>
          <vab-query-form-left-panel :span="6">
            <el-button
              v-if="$perms('system_employee_insert')"
              icon="el-icon-plus"
              type="primary"
              @click="handleInsert"
            > 添加 </el-button>
          </vab-query-form-left-panel>
          <vab-query-form-right-panel :span="18">
            <el-form :inline="true" :model="queryForm" @submit.native.prevent>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm.fullName_LIKE"
                  placeholder="请输入员工姓名"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm.employeeNumber_LIKE"
                  placeholder="请输入员工编号"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryForm.departmentId_EQ"
                  placeholder="请选择部门"
                  clearable
                  style="width: 150px"
                  filterable
                >
                  <el-option
                    v-for="item in deptList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryForm.positionId_EQ"
                  placeholder="请选择职位"
                  clearable
                  style="width: 150px"
                  filterable
                >
                  <el-option
                    v-for="item in positionList"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" type="primary" @click="queryData">
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-right-panel>
        </vab-query-form>

        <el-table
          ref="employeeTable"
          v-loading="listLoading"
          :data="tableData"
          :element-loading-text="elementLoadingText"
          :highlight-current-row="true"
          @current-change="setSelectRows"
        >
          <el-table-column
            show-overflow-tooltip
            prop="employeeNumber"
            label="员工编号"
            min-width="120"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="fullName"
            label="姓名"
            min-width="100"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="gender"
            label="性别"
            min-width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ formatGender(scope.row.gender) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="phoneNumber"
            label="手机号码"
            min-width="120"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="departmentId"
            label="所属部门"
            min-width="120"
            :formatter="formatDepartment"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="positionId"
            label="主职位"
            min-width="120"
            :formatter="formatPosition"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="concurrentPositionId"
            label="兼任职位"
            min-width="120"
            :formatter="formatPosition"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="hireDate"
            label="入职日期"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ formatDate(scope.row.hireDate) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="status"
            label="员工状态"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ formatStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            label="社保"
            min-width="80"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.hasSocialInsurance ? 'success' : 'danger'" size="mini">
                {{ scope.row.hasSocialInsurance ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            label="合同"
            min-width="80"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.isContractSigned ? 'success' : 'danger'" size="mini">
                {{ scope.row.isContractSigned ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            label="转正"
            min-width="80"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.isProbationPassed ? 'success' : 'warning'" size="mini">
                {{ scope.row.isProbationPassed ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            label="资料"
            min-width="80"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.areDocumentsSigned ? 'success' : 'danger'" size="mini">
                {{ scope.row.areDocumentsSigned ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            show-overflow-tooltip
            label="操作"
            width="160"
            v-if="$perms('system_employee_update') || $perms('system_employee_delete')"
          >
            <template v-slot="scope">
              <el-button
                v-if="$perms('system_employee_update')"
                type="text"
                @click="handleUpdate(scope.row)"
              > 编辑 </el-button>

              <el-divider direction="vertical"></el-divider>

              <el-button
                v-if="$perms('system_employee_delete')"
                type="text"
                @click="handleDelete(scope.row)"
              > 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          :current-page="queryForm.pageNo"
          :page-size="queryForm.pageSize"
          :layout="layout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-col>
    </el-row>

    <!-- 编辑 -->
    <employee-edit ref="edit" :dept-list="deptList" :position-list="positionList" @fetchData="fetchData"></employee-edit>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getList, doDelete } from '@/api/system/employee/employeeManagement';
import { getAll as getAllDepts } from '@/api/system/department/deptManagement';
import { getAll as getAllPositions } from '@/api/system/position/posManagement';
import EmployeeEdit from './components/EmployeeManagementEdit'

export default {
  name: "EmployeeManagement",
  components: { EmployeeEdit },
  data () {
    return {
      // 性别字典
      genderDict: {
        '男': '男',
        '女': '女'
      },
      // 员工状态字典
      statusDict: {
        '在职': '在职',
        '离职': '离职',
        '试用期': '试用期'
      },
      // 当前行
      currentRow: null,
      // 字典
      dict: {},
      // 查询表单
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        fullName_LIKE: "",
        employeeNumber_LIKE: "",
        departmentId_EQ: "",
        positionId_EQ: "",
        status_EQ: "",
        tenantId: null,
        dataMonth: null
      },
      // 返回数据
      tableData: [],
      // 部门列表
      deptList: [],
      // 职位列表
      positionList: [],
      // list 加载状态
      listLoading: true,
      // 总条数
      total: 0,
      elementLoadingText: "正在加载...",
      layout: "total, sizes, prev, pager, next, jumper",
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
      selectedMonth: 'month/selectedMonth',
    }),
    readyToFetch() {
      return this.tenantId && this.selectedMonth;
    },
  },
  watch: {
    readyToFetch: {
      handler(isReady) {
        if (isReady) {
          this.fetchData();
          this.fetchAllDepts();
          this.fetchAllPositions();
        }
      },
      immediate: true,
    },
  },
  created() {
    this.dict.employee_status = this.$getDictList("employee_status");
    this.dict.gender = this.$getDictList("gender");
  },
  methods: {
    setSelectRows(val) {
      this.currentRow = val;
    },
    handleInsert() {
      this.$refs["edit"].showEdit(null, this.queryForm.dataMonth);
    },
    handleUpdate(row) {
      if (row.id) {
        this.$refs["edit"].showEdit(row);
      }
    },
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前员工吗", null, async () => {
          const { msg } = await doDelete({ id: row.id });
          this.$baseMessage(msg, "success");
          await this.fetchData();
        });
      }
    },
    handleSizeChange(val) {
      this.queryForm.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val;
      this.fetchData();
    },
    queryData() {
      this.queryForm.pageNo = 1;
      this.fetchData();
    },
    async fetchAllDepts() {
      if (!this.tenantId || !this.selectedMonth) {
        return;
      }
      const { data } = await getAllDepts({
        tenantId: this.tenantId,
        dataMonth: this.selectedMonth,
      });
      this.deptList = data;
    },

    async fetchAllPositions() {
      if (!this.tenantId || !this.selectedMonth) {
        return;
      }
      const { data } = await getAllPositions({
        tenantId: this.tenantId,
        dataMonth: this.selectedMonth,
      });
      this.positionList = data;
    },

    // 格式化部门
    formatDepartment(row, column, cellValue) {
      if (cellValue) {
        const dept = this.deptList.find((dept) => dept.id === cellValue);
        return dept ? dept.name : '';
      }
      return '无';
    },

    // 格式化职位
    formatPosition(row, column, cellValue) {
      if (cellValue) {
        const position = this.positionList.find((pos) => pos.id === cellValue);
        return position ? position.title : '';
      }
      return '无';
    },

    // 格式化性别
    formatGender(gender) {
      return this.genderDict[gender] || gender || '未知';
    },

    // 格式化状态
    formatStatus(status) {
      return this.statusDict[status] || status || '未知';
    },

    // 获取状态类型
    getStatusType(status) {
      switch (status) {
        case '在职':
          return 'success';
        case '试用期':
          return 'warning';
        case '离职':
          return 'danger';
        default:
          return 'info';
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      return d.toLocaleDateString('zh-CN');
    },

    async fetchData() {
      if (!this.tenantId || !this.selectedMonth) {
        return;
      }
      this.queryForm.tenantId = this.tenantId;
      this.queryForm.dataMonth = this.selectedMonth;
      this.listLoading = true;
      const { data } = await getList(this.queryForm);
      console.log("data", data);
      this.tableData = data.rows;
      console.log("tableData", this.tableData);
      this.total = Number(data.total);
      console.log("total", this.total);
      this.listLoading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.employeeManagement-container {
  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
  }
}
</style>