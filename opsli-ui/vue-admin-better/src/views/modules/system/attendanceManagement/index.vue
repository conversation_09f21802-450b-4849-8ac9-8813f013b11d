<template>
  <div class="attendanceManagement-container">
    <el-row :gutter="15">
      <!-- 考勤管理主表 -->
      <el-col>
        <vab-query-form>
          <vab-query-form-left-panel :span="6">
            <el-button
              v-if="$perms('system_attendance_insert')"
              icon="el-icon-plus"
              type="primary"
              @click="handleInsert"
            > 添加 </el-button>
          </vab-query-form-left-panel>
          <vab-query-form-right-panel :span="18">
            <el-form :inline="true" :model="queryForm" @submit.native.prevent>
              <el-form-item>
                <el-select
                  v-model="queryForm.employeeId_EQ"
                  placeholder="请选择员工"
                  clearable
                  style="width: 150px"
                  filterable
                >
                  <el-option
                    v-for="item in employeeList"
                    :key="item.id"
                    :label="item.fullName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryForm.departmentId_EQ"
                  placeholder="请选择部门"
                  clearable
                  style="width: 150px"
                  filterable
                >
                  <el-option
                    v-for="item in deptList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryForm.status_EQ"
                  placeholder="请选择考勤状态"
                  clearable
                  style="width: 150px"
                  filterable
                >
                  <el-option label="正常" value="正常"></el-option>
                  <el-option label="迟到" value="迟到"></el-option>
                  <el-option label="早退" value="早退"></el-option>
                  <el-option label="旷工" value="旷工"></el-option>
                  <el-option label="请假" value="请假"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                  v-model="queryForm.attendanceDate_EQ"
                  type="date"
                  placeholder="选择考勤日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  clearable
                ></el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" type="primary" @click="queryData">
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-right-panel>
        </vab-query-form>

        <el-table
          ref="attendanceTable"
          v-loading="listLoading"
          :data="tableData"
          :element-loading-text="elementLoadingText"
          :highlight-current-row="true"
          @current-change="setSelectRows"
        >
          <el-table-column
            show-overflow-tooltip
            prop="employeeId"
            label="员工姓名"
            min-width="120"
            :formatter="formatEmployee"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="departmentId"
            label="所属部门"
            min-width="120"
            :formatter="formatDepartment"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="attendanceDate"
            label="考勤日期"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ formatDate(scope.row.attendanceDate) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="checkInTime"
            label="签到时间"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ formatTime(scope.row.checkInTime) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="checkOutTime"
            label="签退时间"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ formatTime(scope.row.checkOutTime) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="status"
            label="考勤状态"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            label="纳入统计"
            min-width="80"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.includeInStats ? 'success' : 'danger'" size="mini">
                {{ scope.row.includeInStats ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="remarks"
            label="备注"
            min-width="150"
          ></el-table-column>

          <el-table-column
            fixed="right"
            show-overflow-tooltip
            label="操作"
            width="160"
            v-if="$perms('system_attendance_update') || $perms('system_attendance_delete')"
          >
            <template v-slot="scope">
              <el-button
                v-if="$perms('system_attendance_update')"
                type="text"
                @click="handleUpdate(scope.row)"
              > 编辑 </el-button>

              <el-divider direction="vertical"></el-divider>

              <el-button
                v-if="$perms('system_attendance_delete')"
                type="text"
                @click="handleDelete(scope.row)"
              > 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          :current-page="queryForm.pageNo"
          :page-size="queryForm.pageSize"
          :layout="layout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-col>
    </el-row>

    <!-- 编辑 -->
    <attendance-edit ref="edit" :dept-list="deptList" :employee-list="employeeList" @fetchData="fetchData"></attendance-edit>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getList, doDelete } from '@/api/system/attendance/attendanceManagement';
import { getAll as getAllDepts } from '@/api/system/department/deptManagement';
import { getAll as getAllEmployees } from '@/api/system/employee/employeeManagement';
import AttendanceEdit from './components/AttendanceManagementEdit'

export default {
  name: "AttendanceManagement",
  components: { AttendanceEdit },
  data () {
    return {
      // 考勤状态字典
      statusDict: {
        '正常': '正常',
        '迟到': '迟到',
        '早退': '早退',
        '旷工': '旷工',
        '请假': '请假'
      },
      // 当前行
      currentRow: null,
      // 字典
      dict: {},
      // 查询表单
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        employeeId_EQ: "",
        departmentId_EQ: "",
        status_EQ: "",
        attendanceDate_EQ: "",
        tenantId: null,
        dataMonth: null
      },
      // 返回数据
      tableData: [],
      // 部门列表
      deptList: [],
      // 员工列表
      employeeList: [],
      // list 加载状态
      listLoading: true,
      // 总条数
      total: 0,
      elementLoadingText: "正在加载...",
      layout: "total, sizes, prev, pager, next, jumper",
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
      selectedMonth: 'month/selectedMonth',
    }),
    readyToFetch() {
      return this.tenantId && this.selectedMonth;
    },
  },
  watch: {
    readyToFetch: {
      handler(isReady) {
        if (isReady) {
          this.fetchData();
          this.fetchAllDepts();
          this.fetchAllEmployees();
        }
      },
      immediate: true,
    },
  },
  created() {
    this.dict.attendance_status = this.$getDictList("attendance_status");
  },
  methods: {
    setSelectRows(val) {
      this.currentRow = val;
    },
    handleInsert() {
      this.$refs["edit"].showEdit(null, this.queryForm.dataMonth);
    },
    handleUpdate(row) {
      if (row.id) {
        this.$refs["edit"].showEdit(row);
      }
    },
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前考勤记录吗", null, async () => {
          const { msg } = await doDelete({ id: row.id });
          this.$baseMessage(msg, "success");
          await this.fetchData();
        });
      }
    },
    handleSizeChange(val) {
      this.queryForm.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val;
      this.fetchData();
    },
    queryData() {
      this.queryForm.pageNo = 1;
      this.fetchData();
    },
    async fetchAllDepts() {
      if (!this.tenantId || !this.selectedMonth) {
        return;
      }
      const { data } = await getAllDepts({
        tenantId: this.tenantId,
        dataMonth: this.selectedMonth,
      });
      this.deptList = data;
    },

    async fetchAllEmployees() {
      if (!this.tenantId || !this.selectedMonth) {
        return;
      }
      const { data } = await getAllEmployees({
        tenantId: this.tenantId,
        dataMonth: this.selectedMonth,
      });
      this.employeeList = data;
    },

    // 格式化部门
    formatDepartment(row, column, cellValue) {
      if (cellValue) {
        const dept = this.deptList.find((dept) => dept.id === cellValue);
        return dept ? dept.name : '';
      }
      return '无';
    },

    // 格式化员工
    formatEmployee(row, column, cellValue) {
      if (cellValue) {
        const employee = this.employeeList.find((emp) => emp.id === cellValue);
        return employee ? employee.fullName : '';
      }
      return '无';
    },

    // 获取状态类型
    getStatusType(status) {
      switch (status) {
        case '正常':
          return 'success';
        case '迟到':
        case '早退':
          return 'warning';
        case '旷工':
          return 'danger';
        case '请假':
          return 'info';
        default:
          return 'info';
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      return d.toLocaleDateString('zh-CN');
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '';
      return time;
    },

    async fetchData() {
      if (!this.tenantId || !this.selectedMonth) {
        return;
      }
      this.queryForm.tenantId = this.tenantId;
      this.queryForm.dataMonth = this.selectedMonth;
      this.listLoading = true;
      const { data } = await getList(this.queryForm);
      console.log("data", data);
      this.tableData = data.rows;
      console.log("tableData", this.tableData);
      this.total = Number(data.total);
      console.log("total", this.total);
      this.listLoading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.attendanceManagement-container {
  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
  }
}
</style>