# Department Management Module

## Overview
The Department Management module provides functionality for managing departments with multi-tenant and monthly data segmentation support. It allows users to create, view, edit, and delete departments, as well as manage hierarchical department structures.

## Features
- Multi-tenant department management
- Monthly data segmentation
- Hierarchical department structure
- Department name and code uniqueness validation
- Permission-based access control
- Search and filtering capabilities

## Integration Instructions

### Backend Integration
1. The backend API layer is already implemented in:
   - API Interface: `opsli-boot/opsli-api/src/main/java/org/opsli/api/web/system/department/DepartmentApi.java`
   - Controller: `opsli-boot/opsli-modulars/opsli-modulars-system/src/main/java/org/opsli/modulars/system/department/web/DepartmentRestController.java`

2. The service layer is implemented in:
   - Service Interface: `opsli-boot/opsli-modulars/opsli-modulars-system/src/main/java/org/opsli/modulars/system/department/service/IDepartmentService.java`
   - Service Implementation: `opsli-boot/opsli-modulars/opsli-modulars-system/src/main/java/org/opsli/modulars/system/department/service/impl/DepartmentServiceImpl.java`

3. The data access layer is implemented in:
   - Entity: `opsli-boot/opsli-modulars/opsli-modulars-system/src/main/java/org/opsli/modulars/system/department/entity/DepartmentMonthly.java`
   - Mapper Interface: `opsli-boot/opsli-modulars/opsli-modulars-system/src/main/java/org/opsli/modulars/system/department/mapper/DepartmentMapper.java`
   - Mapper XML: `opsli-boot/opsli-modulars/opsli-modulars-system/src/main/java/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml`

### Frontend Integration
1. The frontend API service is implemented in:
   - `opsli-ui/vue-admin-better/src/api/system/department/deptManagement.js`

2. The frontend components are implemented in:
   - Main Page: `opsli-ui/vue-admin-better/src/views/modules/system/deptManagement/index.vue`
   - Edit Dialog: `opsli-ui/vue-admin-better/src/views/modules/system/deptManagement/components/DeptManagementEdit.vue`

### Menu Integration
To add the Department Management module to the system menu:

1. Execute the SQL script in `menu-setup.sql` to add the necessary menu items and permissions.
2. Alternatively, use the OPSLI system's menu management interface to add the menu manually:
   - Menu Name: 部门管理
   - Menu URL: /system/deptManagement
   - Menu Icon: el-icon-office-building
   - Parent Menu: System Management
   - Permissions: system_department_select, system_department_insert, system_department_update, system_department_delete

3. Clear the application cache or restart the application for the changes to take effect.

## Testing
A comprehensive test plan is provided in `test-plan.md`. Follow the test cases to verify that the Department Management module is working correctly.

## Dependencies
- Month Picker: The module integrates with the global month picker component for month-based data filtering.
- Permission System: The module uses the OPSLI permission system for access control.
- Dictionary System: The module uses the OPSLI dictionary system for status values.

## Troubleshooting
- If the menu doesn't appear after adding it, try clearing the browser cache or logging out and back in.
- If permission errors occur, verify that the user role has the necessary permissions assigned.
- If data doesn't appear for a specific month, verify that data exists for that month in the database.