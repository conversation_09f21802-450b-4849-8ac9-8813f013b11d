# VabTopBar功能增强说明

## 概述
本次更新为VabTopBar组件增加了三个重要功能：
1. 当前账号所属的租户信息显示
2. 当前账号的有效期信息显示
3. 月份选择器，用于控制系统显示数据的月份

## 功能详情

### 1. 租户信息显示
- **位置**: VabTopBar右侧面板
- **显示内容**: 租户名称（如果用户属于某个租户）
- **图标**: 建筑物图标
- **响应式**: 在小屏幕设备上隐藏

### 2. 账户有效期显示
- **位置**: VabTopBar右侧面板
- **显示内容**: 
  - 已过期：红色显示"已过期"
  - 今日到期：红色显示"今日到期"
  - 7天内到期：红色显示"X天后到期"
  - 其他：正常颜色显示具体到期日期
- **图标**: 时钟图标
- **响应式**: 在中等屏幕以下设备上隐藏

### 3. 月份选择器
- **位置**: VabTopBar右侧面板
- **功能**: 选择年月，控制系统显示数据的月份
- **默认值**: 当前年月
- **持久化**: 选择的月份会保存到localStorage
- **事件**: 月份变更时会触发全局事件`month-changed`
- **响应式**: 在超小屏幕设备上隐藏

## 技术实现

### 后端修改
1. **UserInfo.java**: 添加了`tenantName`和`expireTime`字段
2. **UserRestController.java**: 在`getInfoById`方法中添加了租户名称获取逻辑

### 前端修改
1. **user store**: 扩展了状态管理，添加租户信息、有效期信息和当前月份
2. **MonthPicker组件**: 新建的可复用月份选择器组件
3. **VabTopBar组件**: 集成了新功能并优化了响应式布局

### 状态管理
```javascript
// 新增的状态
state: {
  tenantId: "",
  tenantName: "",
  expireTime: null,
  currentMonth: "2024-01", // 格式: YYYY-MM
}

// 新增的getters
getters: {
  tenantId: (state) => state.tenantId,
  tenantName: (state) => state.tenantName,
  expireTime: (state) => state.expireTime,
  currentMonth: (state) => state.currentMonth,
}

// 新增的actions
actions: {
  updateCurrentMonth({ commit }, month) {
    commit("setCurrentMonth", month);
  }
}
```

### 全局事件
- `month-changed`: 当月份选择器的值改变时触发，其他组件可以监听此事件来更新数据

## 使用方法

### 监听月份变更事件
```javascript
// 在组件中监听月份变更
created() {
  this.$baseEventBus.$on('month-changed', (month) => {
    console.log('月份已变更为:', month);
    // 重新加载数据
    this.fetchData();
  });
}
```

### 获取当前选择的月份
```javascript
// 通过Vuex获取
computed: {
  ...mapGetters({
    currentMonth: 'user/currentMonth'
  })
}

// 或者从localStorage获取
const currentMonth = localStorage.getItem('currentMonth');
```

## 响应式设计

### 屏幕尺寸适配
- **xl (≥1200px)**: 显示所有功能
- **lg (≥992px)**: 显示所有功能，租户名称略微缩短
- **md (≥768px)**: 显示所有功能，进一步缩短文本
- **sm (≥576px)**: 隐藏租户信息，保留有效期和月份选择器
- **xs (<576px)**: 只显示月份选择器和基本功能

### 布局调整
- 调整了栅格布局比例：Logo(6) + 菜单(10) + 右侧面板(8)
- 优化了各元素的间距和字体大小

## 测试
创建了测试页面 `src/views/test/TopBarTest.vue` 用于验证功能：
- 显示当前用户信息
- 测试月份选择器
- 模拟数据测试功能

## 注意事项
1. 租户信息和有效期信息依赖于后端API返回的数据
2. 月份选择器的值会持久化到localStorage
3. 在小屏幕设备上，部分信息会隐藏以保持界面简洁
4. 所有新增功能都是向后兼容的，不会影响现有功能
